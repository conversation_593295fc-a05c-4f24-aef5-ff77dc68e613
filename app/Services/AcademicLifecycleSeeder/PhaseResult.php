<?php

namespace App\Services\AcademicLifecycleSeeder;

class PhaseResult
{
    public bool $success;
    public string $phase;
    public string $message;
    public array $data;
    public ?string $errorMessage;
    public array $recordCounts;
    public int $executionTime;

    public function __construct(
        bool $success,
        string $phase,
        string $message,
        array $data = [],
        ?string $errorMessage = null,
        array $recordCounts = [],
        int $executionTime = 0
    ) {
        $this->success = $success;
        $this->phase = $phase;
        $this->message = $message;
        $this->data = $data;
        $this->errorMessage = $errorMessage;
        $this->recordCounts = $recordCounts;
        $this->executionTime = $executionTime;
    }

    /**
     * Create successful phase result
     */
    public static function success(string $phase, string $message, array $data = [], array $recordCounts = [], int $executionTime = 0): self
    {
        return new self(true, $phase, $message, $data, null, $recordCounts, $executionTime);
    }

    /**
     * Create failed phase result
     */
    public static function failure(string $phase, string $message, string $errorMessage, array $data = [], int $executionTime = 0): self
    {
        return new self(false, $phase, $message, $data, $errorMessage, [], $executionTime);
    }

    /**
     * Add record count
     */
    public function addRecordCount(string $type, int $count): void
    {
        $this->recordCounts[$type] = $count;
    }

    /**
     * Get total records created
     */
    public function getTotalRecords(): int
    {
        return array_sum($this->recordCounts);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'phase' => $this->phase,
            'message' => $this->message,
            'data' => $this->data,
            'error_message' => $this->errorMessage,
            'record_counts' => $this->recordCounts,
            'total_records' => $this->getTotalRecords(),
            'execution_time' => $this->executionTime,
        ];
    }
}
