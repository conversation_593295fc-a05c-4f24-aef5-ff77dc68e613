<?php

namespace App\Services\AcademicLifecycleSeeder;

class SeederResult
{
    public bool $success;
    public string $message;
    public ProgressReport $progressReport;
    public array $data;
    public ?string $errorMessage;
    public array $warnings;

    public function __construct(
        bool $success,
        string $message,
        ProgressReport $progressReport,
        array $data = [],
        ?string $errorMessage = null,
        array $warnings = []
    ) {
        $this->success = $success;
        $this->message = $message;
        $this->progressReport = $progressReport;
        $this->data = $data;
        $this->errorMessage = $errorMessage;
        $this->warnings = $warnings;
    }

    /**
     * Create successful result
     */
    public static function success(string $message, ProgressReport $progressReport, array $data = []): self
    {
        return new self(true, $message, $progressReport, $data);
    }

    /**
     * Create failed result
     */
    public static function failure(string $message, ProgressReport $progressReport, string $errorMessage, array $data = []): self
    {
        return new self(false, $message, $progressReport, $data, $errorMessage);
    }

    /**
     * Add warning
     */
    public function addWarning(string $warning): void
    {
        $this->warnings[] = $warning;
    }

    /**
     * Check if result has warnings
     */
    public function hasWarnings(): bool
    {
        return !empty($this->warnings);
    }

    /**
     * Get summary
     */
    public function getSummary(): array
    {
        return [
            'success' => $this->success,
            'message' => $this->message,
            'error_message' => $this->errorMessage,
            'warnings' => $this->warnings,
            'progress' => $this->progressReport->getSummary(),
            'data' => $this->data,
        ];
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'message' => $this->message,
            'progress_report' => $this->progressReport->toArray(),
            'data' => $this->data,
            'error_message' => $this->errorMessage,
            'warnings' => $this->warnings,
        ];
    }
}
