<?php

namespace App\Services\AcademicLifecycleSeeder;

use DateTime;

class ProgressReport
{
    public string $currentPhase;
    public int $totalPhases;
    public int $completedPhases;
    public int $totalRecords;
    public int $processedRecords;
    public array $phaseDetails;
    public ?string $errorMessage;
    public DateTime $startTime;
    public ?DateTime $endTime;
    public array $recordCounts;

    public function __construct()
    {
        $this->currentPhase = '';
        $this->totalPhases = 0;
        $this->completedPhases = 0;
        $this->totalRecords = 0;
        $this->processedRecords = 0;
        $this->phaseDetails = [];
        $this->errorMessage = null;
        $this->startTime = new DateTime();
        $this->endTime = null;
        $this->recordCounts = [];
    }

    /**
     * Update current phase
     */
    public function setCurrentPhase(string $phase): void
    {
        $this->currentPhase = $phase;
    }

    /**
     * Set total phases
     */
    public function setTotalPhases(int $total): void
    {
        $this->totalPhases = $total;
    }

    /**
     * Mark phase as completed
     */
    public function completePhase(string $phase, array $details = []): void
    {
        $this->completedPhases++;
        $this->phaseDetails[$phase] = array_merge([
            'completed_at' => new DateTime(),
            'status' => 'completed',
        ], $details);
    }

    /**
     * Mark phase as failed
     */
    public function failPhase(string $phase, string $error, array $details = []): void
    {
        $this->errorMessage = $error;
        $this->phaseDetails[$phase] = array_merge([
            'completed_at' => new DateTime(),
            'status' => 'failed',
            'error' => $error,
        ], $details);
    }

    /**
     * Update record counts
     */
    public function updateRecordCounts(int $processed, int $total = null): void
    {
        $this->processedRecords = $processed;
        if ($total !== null) {
            $this->totalRecords = $total;
        }
    }

    /**
     * Add record count for specific type
     */
    public function addRecordCount(string $type, int $count): void
    {
        $this->recordCounts[$type] = ($this->recordCounts[$type] ?? 0) + $count;
    }

    /**
     * Set record count for specific type
     */
    public function setRecordCount(string $type, int $count): void
    {
        $this->recordCounts[$type] = $count;
    }

    /**
     * Mark seeder as completed
     */
    public function complete(): void
    {
        $this->endTime = new DateTime();
        $this->currentPhase = 'completed';
    }

    /**
     * Get completion percentage
     */
    public function getCompletionPercentage(): float
    {
        if ($this->totalPhases === 0) {
            return 0.0;
        }

        return ($this->completedPhases / $this->totalPhases) * 100;
    }

    /**
     * Get execution time in seconds
     */
    public function getExecutionTime(): ?int
    {
        if ($this->endTime === null) {
            return (new DateTime())->getTimestamp() - $this->startTime->getTimestamp();
        }

        return $this->endTime->getTimestamp() - $this->startTime->getTimestamp();
    }

    /**
     * Check if seeder is completed
     */
    public function isCompleted(): bool
    {
        return $this->endTime !== null;
    }

    /**
     * Check if seeder has errors
     */
    public function hasErrors(): bool
    {
        return $this->errorMessage !== null;
    }

    /**
     * Get summary statistics
     */
    public function getSummary(): array
    {
        return [
            'status' => $this->isCompleted() ? 'completed' : ($this->hasErrors() ? 'failed' : 'running'),
            'current_phase' => $this->currentPhase,
            'completion_percentage' => $this->getCompletionPercentage(),
            'execution_time' => $this->getExecutionTime(),
            'phases_completed' => $this->completedPhases,
            'total_phases' => $this->totalPhases,
            'records_processed' => $this->processedRecords,
            'total_records' => $this->totalRecords,
            'record_counts' => $this->recordCounts,
            'error_message' => $this->errorMessage,
            'started_at' => $this->startTime->format('Y-m-d H:i:s'),
            'completed_at' => $this->endTime?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'current_phase' => $this->currentPhase,
            'total_phases' => $this->totalPhases,
            'completed_phases' => $this->completedPhases,
            'total_records' => $this->totalRecords,
            'processed_records' => $this->processedRecords,
            'phase_details' => $this->phaseDetails,
            'error_message' => $this->errorMessage,
            'start_time' => $this->startTime->format('Y-m-d H:i:s'),
            'end_time' => $this->endTime?->format('Y-m-d H:i:s'),
            'record_counts' => $this->recordCounts,
            'completion_percentage' => $this->getCompletionPercentage(),
            'execution_time' => $this->getExecutionTime(),
        ];
    }
}
