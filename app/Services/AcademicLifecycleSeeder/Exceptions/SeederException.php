<?php

namespace App\Services\AcademicLifecycleSeeder\Exceptions;

use Exception;

abstract class SeederException extends Exception
{
    protected string $phase;
    protected array $context;

    public function __construct(string $message = "", int $code = 0, ?Exception $previous = null, string $phase = '', array $context = [])
    {
        parent::__construct($message, $code, $previous);
        $this->phase = $phase;
        $this->context = $context;
    }

    public function getPhase(): string
    {
        return $this->phase;
    }

    public function getContext(): array
    {
        return $this->context;
    }

    public function toArray(): array
    {
        return [
            'message' => $this->getMessage(),
            'code' => $this->getCode(),
            'phase' => $this->phase,
            'context' => $this->context,
            'file' => $this->getFile(),
            'line' => $this->getLine(),
        ];
    }
}
