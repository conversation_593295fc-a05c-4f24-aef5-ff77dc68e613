<?php

namespace App\Services\AcademicLifecycleSeeder\Generators;

use App\Models\Student;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;

class RealisticDataGenerator
{
    private SeederConfiguration $configuration;

    public function __construct(SeederConfiguration $configuration)
    {
        $this->configuration = $configuration;
    }

    /**
     * Generate grade distribution for a given number of students
     */
    public function generateGradeDistribution(int $studentCount): array
    {
        $distribution = $this->configuration->gradeDistribution;
        $grades = [];

        // Calculate number of students for each grade
        $gradeCount = [];
        $remaining = $studentCount;

        foreach ($distribution as $grade => $percentage) {
            $count = (int) round(($percentage / 100) * $studentCount);
            $gradeCount[$grade] = $count;
            $remaining -= $count;
        }

        // Adjust for rounding errors
        if ($remaining > 0) {
            $gradeCount['C'] += $remaining;
        } elseif ($remaining < 0) {
            $gradeCount['C'] -= abs($remaining);
        }

        // Generate grades
        foreach ($gradeCount as $grade => $count) {
            for ($i = 0; $i < $count; $i++) {
                $grades[] = $grade;
            }
        }

        // Shuffle to randomize
        shuffle($grades);

        return $grades;
    }

    /**
     * Generate attendance pattern for a student
     */
    public function generateAttendancePattern(Student $student): array
    {
        $patterns = $this->configuration->attendancePatterns;
        $patternType = $this->getRandomWeightedValue($patterns);

        // Define attendance probabilities based on pattern type
        $probabilities = match ($patternType) {
            'excellent' => ['present' => 95, 'late' => 4, 'absent' => 1],
            'good' => ['present' => 85, 'late' => 10, 'absent' => 5],
            'average' => ['present' => 70, 'late' => 15, 'absent' => 15],
            'poor' => ['present' => 50, 'late' => 20, 'absent' => 30],
            default => ['present' => 80, 'late' => 10, 'absent' => 10],
        };

        return [
            'type' => $patternType,
            'probabilities' => $probabilities,
            'student_id' => $student->id,
        ];
    }

    /**
     * Generate performance variation
     */
    public function generatePerformanceVariation(): float
    {
        // Generate a random variation between -10% and +10%
        return (mt_rand(-100, 100) / 1000);
    }

    /**
     * Generate random score based on grade target
     */
    public function generateScoreForGrade(string $targetGrade): float
    {
        $baseScore = match ($targetGrade) {
            'A' => mt_rand(90, 100),
            'B' => mt_rand(80, 89),
            'C' => mt_rand(70, 79),
            'D' => mt_rand(60, 69),
            'F' => mt_rand(0, 59),
            default => mt_rand(0, 100),
        };

        // Apply small random variation
        $variation = $this->generatePerformanceVariation();
        $score = $baseScore * (1 + $variation);

        // Ensure score is within valid range
        return max(0, min(100, $score));
    }

    /**
     * Generate submission time relative to due date
     */
    public function generateSubmissionTime(\DateTime $dueDate, string $studentPattern): \DateTime
    {
        $submissionDate = clone $dueDate;

        // Determine submission behavior based on student pattern
        $behavior = match ($studentPattern) {
            'excellent' => ['early' => 80, 'on_time' => 15, 'late' => 5],
            'good' => ['early' => 60, 'on_time' => 30, 'late' => 10],
            'average' => ['early' => 40, 'on_time' => 40, 'late' => 20],
            'poor' => ['early' => 20, 'on_time' => 40, 'late' => 40],
            default => ['early' => 50, 'on_time' => 30, 'late' => 20],
        };

        $submissionType = $this->getRandomWeightedValue($behavior);

        // Adjust submission date based on type
        switch ($submissionType) {
            case 'early':
                // Submit 1-7 days early
                $submissionDate->modify('-' . mt_rand(1, 7) . ' days');
                break;
            case 'on_time':
                // Submit 0-24 hours before deadline
                $submissionDate->modify('-' . mt_rand(0, 24) . ' hours');
                break;
            case 'late':
                // Submit 1-72 hours after deadline
                $submissionDate->modify('+' . mt_rand(1, 72) . ' hours');
                break;
        }

        return $submissionDate;
    }

    /**
     * Get random value based on weighted probabilities
     */
    private function getRandomWeightedValue(array $weights): string
    {
        $total = array_sum($weights);
        $rand = mt_rand(1, $total);

        $current = 0;
        foreach ($weights as $value => $weight) {
            $current += $weight;
            if ($rand <= $current) {
                return $value;
            }
        }

        // Fallback to first key
        return array_key_first($weights);
    }

    /**
     * Generate realistic class schedule
     */
    public function generateClassSchedule(int $totalHours, int $hoursPerSession): array
    {
        $sessionsCount = ceil($totalHours / $hoursPerSession);
        $schedule = [];

        // Define possible days and time slots
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
        $timeSlots = [
            '08:00-10:00', '10:00-12:00', '13:00-15:00', '15:00-17:00', '17:00-19:00'
        ];

        // Randomly select days and time slots
        $selectedDay = $days[array_rand($days)];
        $selectedTimeSlot = $timeSlots[array_rand($timeSlots)];

        // Generate sessions
        for ($i = 0; $i < $sessionsCount; $i++) {
            $weekNumber = floor($i / 1) + 1; // Assuming 1 session per week

            $schedule[] = [
                'session_number' => $i + 1,
                'week_number' => $weekNumber,
                'day' => $selectedDay,
                'time_slot' => $selectedTimeSlot,
                'duration' => $hoursPerSession,
            ];
        }

        return $schedule;
    }
}
