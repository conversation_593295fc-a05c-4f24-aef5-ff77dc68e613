<?php

namespace App\Services\AcademicLifecycleSeeder\Generators;

use App\Models\Student;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;

class RealisticDataGenerator
{
    /**
     * Constructor - no dependencies needed for basic data generation
     */
    public function __construct()
    {
        // No configuration needed for basic methods
    }

    /**
     * Generate grade distribution for a given number of students
     */
    public function generateGradeDistribution(int $studentCount, array $gradeDistribution = null): array
    {
        $distribution = $gradeDistribution ?? [
            'A' => 15,
            'B' => 35,
            'C' => 35,
            'D' => 10,
            'F' => 5
        ];

        $grades = [];

        // Calculate number of students for each grade
        $gradeCount = [];
        $remaining = $studentCount;

        foreach ($distribution as $grade => $percentage) {
            $count = (int) round(($percentage / 100) * $studentCount);
            $gradeCount[$grade] = $count;
            $remaining -= $count;
        }

        // Adjust for rounding errors
        if ($remaining > 0) {
            $gradeCount['C'] += $remaining;
        } elseif ($remaining < 0) {
            $gradeCount['C'] -= abs($remaining);
        }

        // Generate grades
        foreach ($gradeCount as $grade => $count) {
            for ($i = 0; $i < $count; $i++) {
                $grades[] = $grade;
            }
        }

        // Shuffle to randomize
        shuffle($grades);

        return $grades;
    }

    /**
     * Generate attendance pattern for a student
     */
    public function generateAttendancePattern($student): array
    {
        $patterns = [
            'excellent' => 20,
            'good' => 50,
            'average' => 25,
            'poor' => 5,
        ];

        $patternType = $this->getRandomWeightedValue($patterns);

        // Define attendance probabilities based on pattern type
        $probabilities = match ($patternType) {
            'excellent' => ['present' => 95, 'late' => 4, 'absent' => 1],
            'good' => ['present' => 85, 'late' => 10, 'absent' => 5],
            'average' => ['present' => 70, 'late' => 15, 'absent' => 15],
            'poor' => ['present' => 50, 'late' => 20, 'absent' => 30],
            default => ['present' => 80, 'late' => 10, 'absent' => 10],
        };

        return [
            'type' => $patternType,
            'probabilities' => $probabilities,
            'student_id' => is_object($student) ? $student->id : $student,
        ];
    }

    /**
     * Generate performance variation
     */
    public function generatePerformanceVariation(): float
    {
        // Generate a random variation between -10% and +10%
        return (mt_rand(-100, 100) / 1000);
    }

    /**
     * Generate random score based on grade target
     */
    public function generateScoreForGrade(string $targetGrade): float
    {
        $baseScore = match ($targetGrade) {
            'A' => mt_rand(90, 100),
            'B' => mt_rand(80, 89),
            'C' => mt_rand(70, 79),
            'D' => mt_rand(60, 69),
            'F' => mt_rand(0, 59),
            default => mt_rand(0, 100),
        };

        // Apply small random variation
        $variation = $this->generatePerformanceVariation();
        $score = $baseScore * (1 + $variation);

        // Ensure score is within valid range
        return max(0, min(100, $score));
    }

    /**
     * Generate submission time relative to due date
     */
    public function generateSubmissionTime(\DateTime $dueDate, string $studentPattern): \DateTime
    {
        $submissionDate = clone $dueDate;

        // Determine submission behavior based on student pattern
        $behavior = match ($studentPattern) {
            'excellent' => ['early' => 80, 'on_time' => 15, 'late' => 5],
            'good' => ['early' => 60, 'on_time' => 30, 'late' => 10],
            'average' => ['early' => 40, 'on_time' => 40, 'late' => 20],
            'poor' => ['early' => 20, 'on_time' => 40, 'late' => 40],
            default => ['early' => 50, 'on_time' => 30, 'late' => 20],
        };

        $submissionType = $this->getRandomWeightedValue($behavior);

        // Adjust submission date based on type
        switch ($submissionType) {
            case 'early':
                // Submit 1-7 days early
                $submissionDate->modify('-' . mt_rand(1, 7) . ' days');
                break;
            case 'on_time':
                // Submit 0-24 hours before deadline
                $submissionDate->modify('-' . mt_rand(0, 24) . ' hours');
                break;
            case 'late':
                // Submit 1-72 hours after deadline
                $submissionDate->modify('+' . mt_rand(1, 72) . ' hours');
                break;
        }

        return $submissionDate;
    }

    /**
     * Get random value based on weighted probabilities
     */
    private function getRandomWeightedValue(array $weights): string
    {
        $total = array_sum($weights);
        $rand = mt_rand(1, $total);

        $current = 0;
        foreach ($weights as $value => $weight) {
            $current += $weight;
            if ($rand <= $current) {
                return $value;
            }
        }

        // Fallback to first key
        return array_key_first($weights);
    }

    /**
     * Generate realistic class schedule
     */
    public function generateClassSchedule(int $totalHours, int $hoursPerSession): array
    {
        $sessionsCount = ceil($totalHours / $hoursPerSession);
        $schedule = [];

        // Define possible days and time slots
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
        $timeSlots = [
            '08:00-10:00',
            '10:00-12:00',
            '13:00-15:00',
            '15:00-17:00',
            '17:00-19:00'
        ];

        // Randomly select days and time slots
        $selectedDay = $days[array_rand($days)];
        $selectedTimeSlot = $timeSlots[array_rand($timeSlots)];

        // Generate sessions
        for ($i = 0; $i < $sessionsCount; $i++) {
            $weekNumber = floor($i / 1) + 1; // Assuming 1 session per week

            $schedule[] = [
                'session_number' => $i + 1,
                'week_number' => $weekNumber,
                'day' => $selectedDay,
                'time_slot' => $selectedTimeSlot,
                'duration' => $hoursPerSession,
            ];
        }

        return $schedule;
    }

    /**
     * Get random delivery mode for course offerings
     */
    public function getRandomDeliveryMode(): string
    {
        $modes = [
            'in_person' => 60,
            'online' => 25,
            'hybrid' => 15,
        ];

        return $this->getRandomWeightedValue($modes);
    }

    /**
     * Get realistic course capacity based on unit type
     */
    public function getRealisticCourseCapacity(string $unitType): int
    {
        return match ($unitType) {
            'core' => mt_rand(80, 120),      // Core units tend to have larger classes
            'major' => mt_rand(40, 80),      // Major units medium sized
            'elective' => mt_rand(20, 60),   // Electives tend to be smaller
            default => mt_rand(30, 80),
        };
    }

    /**
     * Get special requirements for curriculum unit
     */
    public function getSpecialRequirements($curriculumUnit): ?string
    {
        $requirements = [
            null,  // Most units have no special requirements
            null,
            null,
            'Laboratory safety training required',
            'Laptop required for all sessions',
            'Field work component included',
            'Group project mandatory',
            'Presentation component required',
        ];

        return $requirements[array_rand($requirements)];
    }

    /**
     * Get realistic total hours for curriculum unit
     */
    public function getRealisticTotalHours($curriculumUnit): int
    {
        // Base on credit points if available
        $creditPoints = $curriculumUnit->unit->credit_points ?? 12.5;

        // Typical ratio: 1 credit point = 8-12 contact hours
        $baseHours = $creditPoints * mt_rand(8, 12);

        return max(24, min(150, intval($baseHours))); // Reasonable bounds
    }

    /**
     * Get realistic hours per session
     */
    public function getRealisticHoursPerSession($curriculumUnit): int
    {
        $sessionTypes = [
            2 => 60,  // 2-hour sessions most common
            3 => 30,  // 3-hour sessions for labs/tutorials
            1 => 10,  // 1-hour sessions less common
        ];

        return $this->getRandomWeightedValue($sessionTypes);
    }

    /**
     * Generate realistic assessment structure for syllabus
     */
    public function generateAssessmentStructure($syllabus): array
    {
        $assessmentTypes = [
            // Common assessment structures that total 100%
            [
                ['name' => 'Assignments', 'type' => 'assignment', 'weight_percentage' => 40, 'description' => 'Regular assignments and homework'],
                ['name' => 'Midterm Exam', 'type' => 'exam', 'weight_percentage' => 25, 'description' => 'Mid-semester examination'],
                ['name' => 'Final Exam', 'type' => 'exam', 'weight_percentage' => 35, 'description' => 'Final examination'],
            ],
            [
                ['name' => 'Project', 'type' => 'project', 'weight_percentage' => 50, 'description' => 'Major project work'],
                ['name' => 'Quizzes', 'type' => 'quiz', 'weight_percentage' => 20, 'description' => 'Regular quizzes'],
                ['name' => 'Final Exam', 'type' => 'exam', 'weight_percentage' => 30, 'description' => 'Final examination'],
            ],
            [
                ['name' => 'Continuous Assessment', 'type' => 'assignment', 'weight_percentage' => 60, 'description' => 'Ongoing assessment tasks'],
                ['name' => 'Final Exam', 'type' => 'exam', 'weight_percentage' => 40, 'description' => 'Final examination'],
            ],
            [
                ['name' => 'Lab Work', 'type' => 'practical', 'weight_percentage' => 30, 'description' => 'Laboratory assignments'],
                ['name' => 'Assignments', 'type' => 'assignment', 'weight_percentage' => 30, 'description' => 'Theory assignments'],
                ['name' => 'Final Exam', 'type' => 'exam', 'weight_percentage' => 40, 'description' => 'Final examination'],
            ],
        ];

        return $assessmentTypes[array_rand($assessmentTypes)];
    }

    /**
     * Generate realistic schedule for course offering
     */
    public function generateRealisticSchedule(string $deliveryMode): array
    {
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
        $timeSlots = [
            ['start' => '08:00', 'end' => '10:00'],
            ['start' => '10:00', 'end' => '12:00'],
            ['start' => '13:00', 'end' => '15:00'],
            ['start' => '15:00', 'end' => '17:00'],
            ['start' => '17:00', 'end' => '19:00'],
        ];

        // For online courses, prefer later times
        if ($deliveryMode === 'online') {
            $timeSlots = array_slice($timeSlots, 2); // Prefer afternoon/evening
        }

        // Select random days (1-2 days per week typically)
        $numDays = mt_rand(1, 2);
        $selectedDays = array_rand(array_flip($days), $numDays);
        if (!is_array($selectedDays)) {
            $selectedDays = [$selectedDays];
        }

        $selectedTimeSlot = $timeSlots[array_rand($timeSlots)];

        return [
            'days' => array_values($selectedDays),
            'start_time' => $selectedTimeSlot['start'],
            'end_time' => $selectedTimeSlot['end'],
        ];
    }

    /**
     * Get random registration method
     */
    public function getRandomRegistrationMethod(): string
    {
        $methods = [
            'online' => 70,
            'advisor' => 20,
            'admin_override' => 10,
        ];

        return $this->getRandomWeightedValue($methods);
    }

    /**
     * Get random date between two dates
     */
    public function getRandomDateBetween(\Carbon\Carbon $startDate, \Carbon\Carbon $endDate): \Carbon\Carbon
    {
        $diffInDays = $startDate->diffInDays($endDate);
        $randomDays = mt_rand(0, $diffInDays);

        return $startDate->copy()->addDays($randomDays);
    }

    /**
     * Generate realistic attendance status for a student and session
     */
    public function generateAttendanceStatus($student, $session): string
    {
        // Realistic attendance distribution
        $attendanceWeights = [
            'present' => 75,    // 75% present
            'late' => 10,       // 10% late
            'excused' => 10,    // 10% excused absence
            'absent' => 5,      // 5% unexcused absence
        ];

        // Adjust based on session type
        if (isset($session->session_type) && in_array($session->session_type, ['exam', 'assessment'])) {
            $attendanceWeights = [
                'present' => 90,
                'late' => 5,
                'excused' => 3,
                'absent' => 2,
            ];
        }

        return $this->getRandomWeightedValue($attendanceWeights);
    }
}
