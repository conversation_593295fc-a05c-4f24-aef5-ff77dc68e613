<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Services\AcademicLifecycleSeeder\PhaseResult;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

abstract class AbstractPhaseProcessor implements PhaseProcessorInterface
{
    protected string $phaseName;
    protected array $recordCounts = [];
    protected array $phaseData = [];
    protected bool $useTransaction = true;
    protected int $batchSize = 100;

    /**
     * Constructor
     */
    public function __construct(string $phaseName)
    {
        $this->phaseName = $phaseName;
    }

    /**
     * Execute the phase processor
     */
    public function execute(SeederConfiguration $configuration): PhaseResult
    {
        $startTime = microtime(true);

        try {
            Log::info("Starting phase: {$this->phaseName}");

            // Set batch size from configuration
            $this->batchSize = $configuration->batchSize;

            // Validate prerequisites
            if (!$this->validatePrerequisites()) {
                throw new PhaseExecutionException(
                    "Prerequisites validation failed for phase: {$this->phaseName}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            // Execute phase with or without transaction
            if ($this->useTransaction) {
                DB::beginTransaction();

                $result = $this->process($configuration);

                DB::commit();
            } else {
                $result = $this->process($configuration);
            }

            $executionTime = (int)((microtime(true) - $startTime) * 1000);

            Log::info("Completed phase: {$this->phaseName}", [
                'execution_time' => $executionTime,
                'record_counts' => $this->recordCounts,
            ]);

            return PhaseResult::success(
                $this->phaseName,
                "Phase {$this->phaseName} completed successfully",
                $this->phaseData,
                $this->recordCounts,
                $executionTime
            );

        } catch (Exception $e) {
            if ($this->useTransaction && DB::transactionLevel() > 0) {
                DB::rollBack();
            }

            $executionTime = (int)((microtime(true) - $startTime) * 1000);

            Log::error("Failed phase: {$this->phaseName}", [
                'error' => $e->getMessage(),
                'execution_time' => $executionTime,
            ]);

            return PhaseResult::failure(
                $this->phaseName,
                "Phase {$this->phaseName} failed",
                $e->getMessage(),
                $this->phaseData,
                $executionTime
            );
        }
    }

    /**
     * Process the phase (to be implemented by concrete classes)
     */
    abstract protected function process(SeederConfiguration $configuration): bool;

    /**
     * Get the phase name
     */
    public function getPhaseName(): string
    {
        return $this->phaseName;
    }

    /**
     * Check if the phase can be executed
     */
    public function canExecute(): bool
    {
        return true;
    }

    /**
     * Validate prerequisites for the phase
     */
    public function validatePrerequisites(): bool
    {
        return true;
    }

    /**
     * Add record count
     */
    protected function addRecordCount(string $type, int $count): void
    {
        $this->recordCounts[$type] = ($this->recordCounts[$type] ?? 0) + $count;
    }

    /**
     * Set record count
     */
    protected function setRecordCount(string $type, int $count): void
    {
        $this->recordCounts[$type] = $count;
    }

    /**
     * Add phase data
     */
    protected function addPhaseData(string $key, $value): void
    {
        $this->phaseData[$key] = $value;
    }

    /**
     * Process records in batches
     */
    protected function processBatch(iterable $items, callable $callback): void
    {
        $batch = [];
        $count = 0;

        foreach ($items as $item) {
            $batch[] = $item;
            $count++;

            if ($count % $this->batchSize === 0) {
                $callback($batch);
                $batch = [];
            }
        }

        // Process remaining items
        if (!empty($batch)) {
            $callback($batch);
        }
    }
}
