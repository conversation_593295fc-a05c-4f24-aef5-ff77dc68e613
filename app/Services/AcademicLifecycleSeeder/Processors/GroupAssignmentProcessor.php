<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\CourseOffering;
use App\Models\CourseRegistration;
use App\Models\AcademicRecord;
use App\Models\Student;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use App\Services\AcademicLifecycleSeeder\Exceptions\DistributionValidationException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * GroupAssignmentProcessor - CRITICAL ROUND-ROBIN DISTRIBUTION
 * 
 * Implements round-robin distribution algorithm for even student assignment across course sections.
 * Ensures maximum difference of 1 student between any two sections.
 * 
 * Requirements:
 * - Split large courses (>55 students) into multiple sections
 * - Use round-robin assignment: student 1 → section A, student 2 → section B, etc.
 * - Maintain enrollment balance with maximum 1 student difference between sections
 * - Update course_registration.course_offering_id for reassigned students
 */
class GroupAssignmentProcessor extends AbstractPhaseProcessor
{
    public function __construct()
    {
        parent::__construct('group_assignment');
    }

    /**
     * Process the group assignment phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting group assignment phase with round-robin distribution');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $this->processSemesterGroupAssignment($semesterCode, $configuration);
        }

        Log::info('Group assignment phase completed successfully');
        return true;
    }

    /**
     * Process group assignment for a specific semester
     */
    private function processSemesterGroupAssignment(string $semesterCode, SeederConfiguration $configuration): void
    {
        // Get course offerings for this semester
        $courseOfferings = CourseOffering::whereHas('semester', function ($query) use ($semesterCode) {
            $query->where('code', $semesterCode);
        })
        ->where('is_active', true)
        ->with(['semester', 'curriculumUnit.unit'])
        ->get();

        if ($courseOfferings->isEmpty()) {
            Log::warning("No course offerings found for semester {$semesterCode}");
            return;
        }

        $this->splitLargeCourses($courseOfferings, $configuration);
    }

    /**
     * Split large courses into manageable sections with round-robin distribution
     * Requirements: 2.3 - Split courses with more than maxStudentsPerSection into sections
     */
    public function splitLargeCourses(Collection $offerings, SeederConfiguration $configuration): Collection
    {
        $maxStudentsPerSection = $configuration->maxStudentsPerSection;
        $sectionsCreated = collect();
        $totalStudentsReassigned = 0;

        foreach ($offerings as $courseOffering) {
            $enrollmentCount = CourseRegistration::where('course_offering_id', $courseOffering->id)->count();
            
            if ($enrollmentCount > $maxStudentsPerSection) {
                Log::info("Splitting large course: {$courseOffering->curriculumUnit->unit->code} with {$enrollmentCount} students");
                
                $result = $this->splitCourseWithRoundRobin($courseOffering, $maxStudentsPerSection);
                $sectionsCreated = $sectionsCreated->merge($result['sections']);
                $totalStudentsReassigned += $result['students_reassigned'];
            }
        }

        $this->addRecordCount('course_sections', $sectionsCreated->count());
        $this->addRecordCount('students_reassigned', $totalStudentsReassigned);

        Log::info("Group assignment completed", [
            'sections_created' => $sectionsCreated->count(),
            'students_reassigned' => $totalStudentsReassigned,
        ]);

        return $sectionsCreated;
    }

    /**
     * Split a course using round-robin distribution algorithm
     * CRITICAL METHOD: Implements even distribution with maximum 1 student difference
     */
    public function splitCourseWithRoundRobin(CourseOffering $originalCourseOffering, int $maxStudentsPerSection): array
    {
        // Get all students registered for this course
        $registrations = CourseRegistration::where('course_offering_id', $originalCourseOffering->id)
            ->with('student')
            ->get();

        $totalStudents = $registrations->count();
        $sectionsNeeded = ceil($totalStudents / $maxStudentsPerSection);

        if ($sectionsNeeded <= 1) {
            return ['sections' => collect(), 'students_reassigned' => 0];
        }

        Log::info("Splitting course with round-robin distribution", [
            'course' => $originalCourseOffering->curriculumUnit->unit->code,
            'total_students' => $totalStudents,
            'sections_needed' => $sectionsNeeded,
            'max_per_section' => $maxStudentsPerSection,
        ]);

        // Create additional sections
        $allSections = collect([$originalCourseOffering]);
        $sectionLetters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];

        // Update original to be section A
        $originalCourseOffering->update(['section_code' => $sectionLetters[0]]);

        // Create additional sections (B, C, D, etc.)
        for ($i = 1; $i < $sectionsNeeded; $i++) {
            $newSection = $this->createCourseSectionOffering($originalCourseOffering, $sectionLetters[$i]);
            $allSections->push($newSection);
        }

        // Apply round-robin distribution
        $studentsReassigned = $this->distributeStudentsRoundRobin($registrations, $allSections);

        // Validate even distribution
        $this->validateEvenDistribution($allSections);

        return [
            'sections' => $allSections->slice(1), // Return only new sections (excluding original)
            'students_reassigned' => $studentsReassigned,
        ];
    }

    /**
     * Distribute students evenly across sections using round-robin assignment
     * CRITICAL ALGORITHM: Ensures maximum difference of 1 student between sections
     * Requirements: Round-robin assignment (student 1 → A, student 2 → B, student 3 → C, student 4 → A, etc.)
     */
    public function distributeStudentsRoundRobin(Collection $registrations, Collection $sections): int
    {
        $sectionCount = $sections->count();
        $currentSectionIndex = 0;
        $studentsReassigned = 0;

        Log::info("Starting round-robin distribution", [
            'total_students' => $registrations->count(),
            'total_sections' => $sectionCount,
        ]);

        foreach ($registrations as $index => $registration) {
            $targetSection = $sections->get($currentSectionIndex);
            
            // Only reassign if student is not already in the correct section
            if ($registration->course_offering_id !== $targetSection->id) {
                $this->reassignStudentToSection($registration, $targetSection);
                $studentsReassigned++;
            }

            // Move to next section in round-robin fashion
            $currentSectionIndex = ($currentSectionIndex + 1) % $sectionCount;
        }

        // Update enrollment counts for all sections
        $this->updateSectionEnrollmentCounts($sections);

        Log::info("Round-robin distribution completed", [
            'students_reassigned' => $studentsReassigned,
        ]);

        return $studentsReassigned;
    }

    /**
     * Validate that students are evenly distributed across sections
     * Requirements: Ensure maximum difference of 1 student between any two sections
     */
    public function validateEvenDistribution(Collection $sections): bool
    {
        $enrollmentCounts = [];
        
        foreach ($sections as $section) {
            $count = CourseRegistration::where('course_offering_id', $section->id)->count();
            $enrollmentCounts[] = $count;
        }

        $maxCount = max($enrollmentCounts);
        $minCount = min($enrollmentCounts);
        $maxDifference = $maxCount - $minCount;

        Log::info("Distribution validation", [
            'section_counts' => $enrollmentCounts,
            'max_difference' => $maxDifference,
        ]);

        if ($maxDifference > 1) {
            throw new DistributionValidationException(
                "Uneven distribution detected: max difference is {$maxDifference}, expected ≤ 1"
            );
        }

        return true;
    }

    /**
     * Create a new course section offering
     */
    private function createCourseSectionOffering(CourseOffering $originalOffering, string $sectionCode): CourseOffering
    {
        return CourseOffering::create([
            'semester_id' => $originalOffering->semester_id,
            'curriculum_unit_id' => $originalOffering->curriculum_unit_id,
            'lecture_id' => $originalOffering->lecture_id,
            'section_code' => $sectionCode,
            'max_capacity' => $originalOffering->max_capacity,
            'current_enrollment' => 0, // Will be updated after distribution
            'waitlist_capacity' => $originalOffering->waitlist_capacity,
            'current_waitlist' => 0,
            'delivery_mode' => $originalOffering->delivery_mode,
            'is_active' => true,
            'enrollment_status' => $originalOffering->enrollment_status,
            'registration_start_date' => $originalOffering->registration_start_date,
            'registration_end_date' => $originalOffering->registration_end_date,
            'schedule_days' => $originalOffering->schedule_days,
            'schedule_time_start' => $originalOffering->schedule_time_start,
            'schedule_time_end' => $originalOffering->schedule_time_end,
            'location' => $originalOffering->location . " (Section {$sectionCode})",
        ]);
    }

    /**
     * Reassign student registration to new section
     * Requirements: Update course_registration.course_offering_id for reassigned students
     */
    private function reassignStudentToSection(CourseRegistration $registration, CourseOffering $newSection): void
    {
        $oldSectionId = $registration->course_offering_id;
        
        // Update the registration to point to the new section
        $registration->update(['course_offering_id' => $newSection->id]);

        // Update any associated academic records
        AcademicRecord::where('student_id', $registration->student_id)
            ->where('course_offering_id', $oldSectionId)
            ->where('semester_id', $registration->semester_id)
            ->update(['course_offering_id' => $newSection->id]);

        Log::debug("Reassigned student to section", [
            'student_id' => $registration->student->student_id ?? 'unknown',
            'section_code' => $newSection->section_code,
            'old_offering_id' => $oldSectionId,
            'new_offering_id' => $newSection->id,
        ]);
    }

    /**
     * Update enrollment counts for all sections
     */
    private function updateSectionEnrollmentCounts(Collection $sections): void
    {
        foreach ($sections as $section) {
            $enrollmentCount = CourseRegistration::where('course_offering_id', $section->id)->count();
            $section->update(['current_enrollment' => $enrollmentCount]);
        }
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have course registrations to work with
        $registrationsCount = CourseRegistration::count();
        if ($registrationsCount === 0) {
            Log::warning('No course registrations found for group assignment');
            return false;
        }

        Log::info("Group assignment prerequisites validated", [
            'course_registrations' => $registrationsCount,
        ]);

        return true;
    }

    /**
     * Rollback the group assignment phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back group assignment phase");
        
        // In a real implementation, you would:
        // 1. Restore original course_offering_id for reassigned students
        // 2. Delete created course sections
        // 3. Update enrollment counts
        
        Log::warning("Group assignment rollback requested - manual cleanup may be required");
    }
}
