<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\Student;
use App\Models\CourseOffering;
use App\Models\CourseRegistration;
use App\Models\AcademicRecord;
use App\Models\Semester;
use App\Models\CurriculumUnit;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class StudentRegistrationProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('student_registration');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the student registration phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting student registration phase');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $semester = $this->getSemester($semesterCode);
            if (!$semester) {
                throw new PhaseExecutionException(
                    "Semester not found: {$semesterCode}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            $this->processSemesterRegistrations($semester, $configuration);
        }

        Log::info('Student registration phase completed successfully');
        return true;
    }

    /**
     * Get semester by code
     */
    private function getSemester(string $semesterCode): ?Semester
    {
        return Semester::where('code', $semesterCode)->first();
    }

    /**
     * Process registrations for a specific semester
     */
    private function processSemesterRegistrations(Semester $semester, SeederConfiguration $configuration): void
    {
        Log::info("Processing registrations for semester: {$semester->code}");

        // Get all active students
        $students = Student::where('status', 'active')
            ->whereNotNull('curriculum_version_id')
            ->with(['curriculumVersion', 'campus', 'program'])
            ->get();

        if ($students->isEmpty()) {
            Log::warning("No active students found for registration");
            return;
        }

        // Get available course offerings for this semester
        $courseOfferings = CourseOffering::where('semester_id', $semester->id)
            ->where('is_active', true)
            ->with(['curriculumUnit.unit', 'semester'])
            ->get()
            ->keyBy('curriculum_unit_id');

        if ($courseOfferings->isEmpty()) {
            Log::warning("No course offerings found for semester {$semester->code}");
            return;
        }

        $registrationsCreated = 0;
        $academicRecordsCreated = 0;

        // Process each student
        foreach ($students as $student) {
            $studentResult = $this->registerStudentForCourses($student, $semester, $courseOfferings, $configuration);
            $registrationsCreated += $studentResult['registrations_created'];
            $academicRecordsCreated += $studentResult['academic_records_created'];
        }

        $this->addRecordCount('course_registrations', $registrationsCreated);
        $this->addRecordCount('academic_records', $academicRecordsCreated);

        Log::info("Semester {$semester->code} completed: {$registrationsCreated} registrations, {$academicRecordsCreated} academic records");
    }

    /**
     * Register a student for appropriate courses
     */
    private function registerStudentForCourses(Student $student, Semester $semester, Collection $courseOfferings, SeederConfiguration $configuration): array
    {
        $registrationsCreated = 0;
        $academicRecordsCreated = 0;

        // Get curriculum units for this student that should be taken in current semester
        $eligibleCurriculumUnits = $this->getEligibleCurriculumUnits($student, $semester);

        if ($eligibleCurriculumUnits->isEmpty()) {
            return [
                'registrations_created' => 0,
                'academic_records_created' => 0,
            ];
        }

        foreach ($eligibleCurriculumUnits as $curriculumUnit) {
            // Check if course offering exists for this curriculum unit
            if (!$courseOfferings->has($curriculumUnit->id)) {
                continue;
            }

            $courseOffering = $courseOfferings->get($curriculumUnit->id);

            // Check if student is already registered for this course
            $existingRegistration = CourseRegistration::where('student_id', $student->id)
                ->where('course_offering_id', $courseOffering->id)
                ->where('semester_id', $semester->id)
                ->exists();

            if ($existingRegistration) {
                continue;
            }

            // Validate prerequisites
            if (!$this->validateStudentPrerequisites($student, $curriculumUnit)) {
                continue;
            }

            // Check capacity (with some realistic overbooking)
            if (!$this->checkCourseCapacity($courseOffering, $configuration)) {
                continue;
            }

            try {
                // Create course registration
                $registration = $this->createCourseRegistration($student, $courseOffering, $semester, $curriculumUnit);
                $registrationsCreated++;

                // Create corresponding academic record
                $academicRecord = $this->createAcademicRecord($student, $courseOffering, $semester, $curriculumUnit, $registration);
                $academicRecordsCreated++;

                // Update course offering enrollment count
                $courseOffering->increment('current_enrollment');
            } catch (\Exception $e) {
                Log::error("Failed to register student {$student->student_id} for course {$curriculumUnit->unit->unit_code}: " . $e->getMessage());
                continue;
            }
        }

        return [
            'registrations_created' => $registrationsCreated,
            'academic_records_created' => $academicRecordsCreated,
        ];
    }

    /**
     * Get eligible curriculum units for a student in a semester
     */
    private function getEligibleCurriculumUnits(Student $student, Semester $semester): Collection
    {
        // Determine semester number for this student
        $semesterNumber = $this->calculateStudentSemesterNumber($student, $semester);

        // Get curriculum units for this semester number in student's curriculum version
        $curriculumUnits = CurriculumUnit::where('curriculum_version_id', $student->curriculum_version_id)
            ->where('semester_number', $semesterNumber)
            ->with(['unit'])
            ->get();

        // Filter out units that the student has already completed successfully
        $completedUnitIds = AcademicRecord::where('student_id', $student->id)
            ->whereIn('grade_status', ['completed', 'passed'])
            ->pluck('unit_id')
            ->toArray();

        return $curriculumUnits->filter(function ($curriculumUnit) use ($completedUnitIds) {
            return !in_array($curriculumUnit->unit_id, $completedUnitIds);
        });
    }

    /**
     * Calculate the semester number for a student
     */
    private function calculateStudentSemesterNumber(Student $student, Semester $semester): int
    {
        // Count the number of distinct semesters the student has been enrolled in
        $previousSemestersCount = CourseRegistration::where('student_id', $student->id)
            ->join('semesters', 'course_registrations.semester_id', '=', 'semesters.id')
            ->where('semesters.start_date', '<', $semester->start_date)
            ->distinct('semester_id')
            ->count();

        return $previousSemestersCount + 1;
    }

    /**
     * Validate prerequisites for a student and curriculum unit
     */
    private function validateStudentPrerequisites(Student $student, CurriculumUnit $curriculumUnit): bool
    {
        $unit = $curriculumUnit->unit;

        // If no prerequisites defined, student is eligible
        if (empty($unit->prerequisites)) {
            return true;
        }

        // Get completed units for this student
        $completedUnitCodes = AcademicRecord::where('student_id', $student->id)
            ->whereIn('grade_status', ['completed', 'passed'])
            ->join('units', 'academic_records.unit_id', '=', 'units.id')
            ->pluck('units.unit_code')
            ->toArray();

        // Parse prerequisites (simple implementation - assumes comma-separated unit codes)
        $prerequisiteUnits = array_map('trim', explode(',', $unit->prerequisites));

        // Check if all prerequisite units are completed
        foreach ($prerequisiteUnits as $prerequisiteUnit) {
            if (!in_array($prerequisiteUnit, $completedUnitCodes)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if course has available capacity
     */
    private function checkCourseCapacity(CourseOffering $courseOffering, SeederConfiguration $configuration): bool
    {
        $allowOverbooking = $configuration->allowOverbooking ?? true;
        $overbookingPercentage = 0.1; // 10% overbooking

        if (!$allowOverbooking) {
            return $courseOffering->current_enrollment < $courseOffering->max_capacity;
        }

        $maxWithOverbooking = $courseOffering->max_capacity * (1 + $overbookingPercentage);
        return $courseOffering->current_enrollment < $maxWithOverbooking;
    }

    /**
     * Create a course registration record
     */
    private function createCourseRegistration(Student $student, CourseOffering $courseOffering, Semester $semester, CurriculumUnit $curriculumUnit): CourseRegistration
    {
        $registrationDate = $this->generateRegistrationDate($semester);
        $registrationMethod = $this->dataGenerator->getRandomRegistrationMethod();

        return CourseRegistration::create([
            'student_id' => $student->id,
            'course_offering_id' => $courseOffering->id,
            'semester_id' => $semester->id,
            'registration_status' => 'registered',
            'registration_date' => $registrationDate,
            'registration_method' => $registrationMethod,
            'credit_hours' => $curriculumUnit->unit->credit_points ?? 3,
            'attempt_number' => $this->calculateAttemptNumber($student, $curriculumUnit->unit_id),
            'is_retake' => $this->isRetakeAttempt($student, $curriculumUnit->unit_id),
            'notes' => 'Generated by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Create an academic record for the registration
     */
    private function createAcademicRecord(Student $student, CourseOffering $courseOffering, Semester $semester, CurriculumUnit $curriculumUnit, CourseRegistration $registration): AcademicRecord
    {
        return AcademicRecord::create([
            'student_id' => $student->id,
            'course_offering_id' => $courseOffering->id,
            'semester_id' => $semester->id,
            'unit_id' => $curriculumUnit->unit_id,
            'program_id' => $student->program_id,
            'campus_id' => $student->campus_id,
            'credit_hours' => $curriculumUnit->unit->credit_points ?? 3,
            'grade_status' => 'in_progress',
            'completion_status' => 'enrolled',
            'enrollment_date' => $registration->registration_date,
            'is_repeat_course' => $registration->is_retake,
            'attempt_number' => $registration->attempt_number,
            'instructor_id' => $courseOffering->lecturer_id,
            'affects_academic_standing' => true,
            'affects_graduation_requirement' => true,
            'satisfies_prerequisite' => true,
            'administrative_notes' => 'Academic record created by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Generate a realistic registration date
     */
    private function generateRegistrationDate(Semester $semester): Carbon
    {
        // Registration typically happens 1-2 months before semester starts
        $registrationStart = $semester->start_date->copy()->subMonths(2);
        $registrationEnd = $semester->start_date->copy()->subWeeks(2);

        return $this->dataGenerator->getRandomDateBetween($registrationStart, $registrationEnd);
    }

    /**
     * Calculate attempt number for a student and unit
     */
    private function calculateAttemptNumber(Student $student, int $unitId): int
    {
        $attempts = AcademicRecord::where('student_id', $student->id)
            ->where('unit_id', $unitId)
            ->count();

        return $attempts + 1;
    }

    /**
     * Check if this is a retake attempt
     */
    private function isRetakeAttempt(Student $student, int $unitId): bool
    {
        return AcademicRecord::where('student_id', $student->id)
            ->where('unit_id', $unitId)
            ->exists();
    }

    /**
     * Rollback the student registration phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back student registration phase: {$this->phaseName}");

        // Note: Rollback implementation would be complex as it needs to identify
        // which records were created by the seeder vs. manual entries
        // For now, we log the rollback request
    }
}
