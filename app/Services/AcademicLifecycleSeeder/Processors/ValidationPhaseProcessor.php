<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\Semester;
use App\Models\Student;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Exceptions\DataValidationException;
use Illuminate\Support\Facades\DB;

class ValidationPhaseProcessor extends AbstractPhaseProcessor
{
    public function __construct()
    {
        parent::__construct('validation');
        $this->useTransaction = false; // No need for transaction in validation phase
    }

    /**
     * Process the validation phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        // Validate target semesters exist
        $this->validateSemesters($configuration->targetSemesters);

        // Validate students exist
        $this->validateStudents();

        // Validate curriculum data exists
        $this->validateCurriculum();

        return true;
    }

    /**
     * Validate that specified semesters exist
     */
    private function validateSemesters(array $semesterCodes): void
    {
        $validSemesters = [];
        $invalidSemesters = [];

        foreach ($semesterCodes as $semesterCode) {
            $semester = Semester::where('code', $semesterCode)->first();

            if ($semester) {
                $validSemesters[] = $semester;
            } else {
                $invalidSemesters[] = $semesterCode;
            }
        }

        if (!empty($invalidSemesters)) {
            throw new DataValidationException(
                "Invalid semester codes: " . implode(', ', $invalidSemesters),
                0,
                null,
                $this->phaseName,
                ['invalid_semesters' => $invalidSemesters]
            );
        }

        $this->addPhaseData('semesters', $validSemesters);
        $this->setRecordCount('validated_semesters', count($validSemesters));
    }

    /**
     * Validate that students exist in the system
     */
    private function validateStudents(): void
    {
        $studentCount = Student::count();

        if ($studentCount === 0) {
            throw new DataValidationException(
                "No students found in database",
                0,
                null,
                $this->phaseName
            );
        }

        $this->addPhaseData('student_count', $studentCount);
        $this->setRecordCount('validated_students', $studentCount);
    }

    /**
     * Validate that curriculum data exists
     */
    private function validateCurriculum(): void
    {
        $curriculumCount = DB::table('curriculum_versions')->count();

        if ($curriculumCount === 0) {
            throw new DataValidationException(
                "No curriculum versions found",
                0,
                null,
                $this->phaseName
            );
        }

        $this->addPhaseData('curriculum_count', $curriculumCount);
        $this->setRecordCount('validated_curriculum', $curriculumCount);
    }

    /**
     * Rollback the phase execution
     */
    public function rollback(): void
    {
        // No rollback needed for validation phase
        // This phase only validates data, it doesn't create any records
        Log::info("Rollback requested for validation phase - no action needed");
    }
}