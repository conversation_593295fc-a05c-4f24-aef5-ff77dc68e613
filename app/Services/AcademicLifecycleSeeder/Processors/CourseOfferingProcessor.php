<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\CourseOffering;
use App\Models\CurriculumUnit;
use App\Models\Semester;
use App\Models\Student;
use App\Models\Lecture;
use App\Models\Syllabus;
use App\Models\AssessmentComponent;
use App\Models\Building;
use App\Models\Room;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CourseOfferingProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('course_offerings');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the course offering generation phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting course offering generation');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $semester = $this->getSemester($semesterCode);
            if (!$semester) {
                throw new PhaseExecutionException(
                    "Semester not found: {$semesterCode}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            $this->processSemester($semester, $configuration);
        }

        return true;
    }

    /**
     * Process course offerings for a specific semester
     */
    private function processSemester(Semester $semester, SeederConfiguration $configuration): void
    {
        Log::info("Processing semester: {$semester->code}");

        // Get all curriculum units that need course offerings for this semester
        $curriculumUnits = $this->getActiveCurriculumUnitsForSemester($semester);
        Log::info("Found {$curriculumUnits->count()} curriculum units for semester {$semester->code}");

        $createdOfferings = 0;
        $createdSyllabi = 0;
        $createdAssessments = 0;

        $this->processBatch($curriculumUnits, function (array $batch) use ($semester, $configuration, &$createdOfferings, &$createdSyllabi, &$createdAssessments) {
            foreach ($batch as $curriculumUnit) {
                try {
                    // Create course offering
                    $courseOffering = $this->createCourseOffering($curriculumUnit, $semester, $configuration);
                    $createdOfferings++;

                    // Create or update syllabus
                    $syllabus = $this->createOrUpdateSyllabus($curriculumUnit, $semester);
                    if ($syllabus->wasRecentlyCreated) {
                        $createdSyllabi++;
                    }

                    // Create assessment components if needed
                    $assessmentCount = $this->createAssessmentComponents($syllabus, $configuration);
                    $createdAssessments += $assessmentCount;

                    // Assign schedule and location
                    $this->assignScheduleAndLocation($courseOffering, $configuration);
                } catch (\Exception $e) {
                    Log::warning("Failed to create course offering for curriculum unit {$curriculumUnit->id}: " . $e->getMessage());
                }
            }
        });

        $this->addRecordCount('course_offerings', $createdOfferings);
        $this->addRecordCount('syllabi', $createdSyllabi);
        $this->addRecordCount('assessment_components', $createdAssessments);

        Log::info("Semester processing complete", [
            'semester' => $semester->code,
            'course_offerings' => $createdOfferings,
            'syllabi' => $createdSyllabi,
            'assessment_components' => $createdAssessments,
        ]);
    }

    /**
     * Get active curriculum units for students in the semester
     */
    private function getActiveCurriculumUnitsForSemester(Semester $semester): Collection
    {
        // Get all students who are active and need courses for this semester
        $students = Student::where('academic_status', 'active')
            ->whereHas('curriculumVersion')
            ->with(['curriculumVersion.curriculumUnits.unit'])
            ->get();

        // Collect all unique curriculum units these students need
        $curriculumUnitIds = collect();

        foreach ($students as $student) {
            $studentCurriculumUnits = $student->curriculumVersion->curriculumUnits()
                ->with('unit')
                ->get();

            foreach ($studentCurriculumUnits as $curriculumUnit) {
                // Determine if this unit should be offered in this semester
                if ($this->shouldOfferUnitInSemester($curriculumUnit, $semester, $student)) {
                    $curriculumUnitIds->push($curriculumUnit->id);
                }
            }
        }

        // Get unique curriculum units and load relationships
        return CurriculumUnit::whereIn('id', $curriculumUnitIds->unique())
            ->with(['unit', 'curriculumVersion', 'semester'])
            ->get();
    }

    /**
     * Determine if a curriculum unit should be offered in a semester
     */
    private function shouldOfferUnitInSemester(CurriculumUnit $curriculumUnit, Semester $semester, Student $student): bool
    {
        // Check if unit is already offered in this semester
        $existingOffering = CourseOffering::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('semester_id', $semester->id)
            ->exists();

        if ($existingOffering) {
            return false; // Already offered
        }

        // Basic logic: offer units based on student's current year/semester progression
        // This is simplified - in reality would be more complex based on prerequisites, etc.

        // For now, offer all core units and some electives
        if ($curriculumUnit->type === 'core') {
            return true;
        }

        if ($curriculumUnit->type === 'major') {
            return true;
        }

        // Offer some electives randomly
        if ($curriculumUnit->type === 'elective') {
            return rand(1, 100) <= 30; // 30% chance for electives
        }

        return false;
    }

    /**
     * Create a course offering for a curriculum unit
     */
    private function createCourseOffering(CurriculumUnit $curriculumUnit, Semester $semester, SeederConfiguration $configuration): CourseOffering
    {
        // Assign a lecturer
        $lecturer = $this->assignLecturer($curriculumUnit, $configuration);

        // Determine delivery mode
        $deliveryMode = $this->dataGenerator->getRandomDeliveryMode();

        // Set capacity
        $maxCapacity = $this->dataGenerator->getRealisticCourseCapacity($curriculumUnit->type);

        $courseOffering = CourseOffering::create([
            'semester_id' => $semester->id,
            'curriculum_unit_id' => $curriculumUnit->id,
            'lecture_id' => $lecturer?->id,
            'section_code' => null, // Will be set later if needed for large courses
            'max_capacity' => $maxCapacity,
            'current_enrollment' => 0,
            'waitlist_capacity' => min(20, intval($maxCapacity * 0.2)), // 20% of capacity or max 20
            'current_waitlist' => 0,
            'delivery_mode' => $deliveryMode,
            'is_active' => true,
            'enrollment_status' => 'open',
            'registration_start_date' => $semester->registration_start_date,
            'registration_end_date' => $semester->registration_end_date,
            'special_requirements' => $this->dataGenerator->getSpecialRequirements($curriculumUnit),
            'notes' => null,
        ]);

        Log::debug("Created course offering", [
            'id' => $courseOffering->id,
            'unit_code' => $curriculumUnit->unit->code,
            'lecturer' => $lecturer?->display_name,
            'capacity' => $maxCapacity,
        ]);

        return $courseOffering;
    }

    /**
     * Assign a lecturer to a course offering
     */
    private function assignLecturer(CurriculumUnit $curriculumUnit, SeederConfiguration $configuration): ?Lecture
    {
        // Get available lecturers
        $availableLecturers = Lecture::active()
            ->availableForAssignment()
            ->get();

        if ($availableLecturers->isEmpty()) {
            Log::warning("No available lecturers found for curriculum unit {$curriculumUnit->id}");
            return null;
        }

        // For now, assign randomly - could be improved with expertise matching
        return $availableLecturers->random();
    }

    /**
     * Create or update syllabus for curriculum unit
     */
    private function createOrUpdateSyllabus(CurriculumUnit $curriculumUnit, Semester $semester): Syllabus
    {
        // Check if syllabus already exists
        $existingSyllabus = Syllabus::where('curriculum_unit_id', $curriculumUnit->id)
            ->where('is_active', true)
            ->first();

        if ($existingSyllabus) {
            return $existingSyllabus;
        }

        // Create new syllabus
        $syllabus = Syllabus::create([
            'curriculum_unit_id' => $curriculumUnit->id,
            'version' => '1.0',
            'description' => 'Generated syllabus for ' . $curriculumUnit->unit->name,
            'total_hours' => $this->dataGenerator->getRealisticTotalHours($curriculumUnit),
            'hours_per_session' => $this->dataGenerator->getRealisticHoursPerSession($curriculumUnit),
            'is_active' => true,
        ]);

        Log::debug("Created syllabus", [
            'id' => $syllabus->id,
            'curriculum_unit_id' => $curriculumUnit->id,
            'total_hours' => $syllabus->total_hours,
        ]);

        return $syllabus;
    }

    /**
     * Create assessment components for syllabus
     */
    private function createAssessmentComponents(Syllabus $syllabus, SeederConfiguration $configuration): int
    {
        // Check if assessment components already exist
        $existingComponents = $syllabus->assessmentComponents()->count();
        if ($existingComponents > 0) {
            return 0; // Already has components
        }

        // Generate realistic assessment structure
        $assessmentStructure = $this->dataGenerator->generateAssessmentStructure($syllabus);

        $createdCount = 0;
        foreach ($assessmentStructure as $componentData) {
            AssessmentComponent::create([
                'syllabus_id' => $syllabus->id,
                'name' => $componentData['name'],
                'type' => $componentData['type'],
                'weight_percentage' => $componentData['weight_percentage'],
                'description' => $componentData['description'] ?? null,
            ]);
            $createdCount++;
        }

        Log::debug("Created assessment components", [
            'syllabus_id' => $syllabus->id,
            'count' => $createdCount,
        ]);

        return $createdCount;
    }

    /**
     * Assign schedule and location to course offering
     */
    private function assignScheduleAndLocation(CourseOffering $courseOffering, SeederConfiguration $configuration): void
    {
        // Generate realistic schedule
        $schedule = $this->dataGenerator->generateRealisticSchedule($courseOffering->delivery_mode);

        // Get location if in-person or hybrid
        $location = null;
        if (in_array($courseOffering->delivery_mode, ['in_person', 'hybrid'])) {
            $location = $this->assignLocation($courseOffering);
        }

        $courseOffering->update([
            'schedule_days' => $schedule['days'],
            'schedule_time_start' => $schedule['start_time'],
            'schedule_time_end' => $schedule['end_time'],
            'location' => $location,
        ]);

        Log::debug("Assigned schedule and location", [
            'course_offering_id' => $courseOffering->id,
            'days' => $schedule['days'],
            'time' => $schedule['start_time'] . ' - ' . $schedule['end_time'],
            'location' => $location,
        ]);
    }

    /**
     * Assign a room location for in-person courses
     */
    private function assignLocation(CourseOffering $courseOffering): ?string
    {
        // Get available rooms - simplified for now
        $buildings = Building::with('rooms')->get();

        if ($buildings->isEmpty()) {
            return 'TBA'; // To be assigned
        }

        $allRooms = $buildings->flatMap(function ($building) {
            return $building->rooms->map(function ($room) use ($building) {
                return $building->name . ' - ' . $room->name;
            });
        });

        if ($allRooms->isEmpty()) {
            return 'TBA';
        }

        return $allRooms->random();
    }

    /**
     * Get semester by code
     */
    private function getSemester(string $semesterCode): ?Semester
    {
        return Semester::where('code', $semesterCode)->first();
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have necessary data
        $studentsCount = Student::where('academic_status', 'active')->count();
        if ($studentsCount === 0) {
            Log::warning('No active students found for course offering generation');
            return false;
        }

        $lecturersCount = Lecture::active()->count();
        if ($lecturersCount === 0) {
            Log::warning('No active lecturers found for course offering generation');
            // Don't fail - we can create offerings without lecturers initially
        }

        return true;
    }

    /**
     * Rollback the phase execution
     */
    public function rollback(): void
    {
        // Implementation for rollback would go here
        // For now, we'll log the rollback attempt
        Log::info("Rollback requested for course offerings phase");

        // In a real implementation, you might:
        // 1. Delete created course offerings
        // 2. Delete created syllabi
        // 3. Delete created assessment components
        // 4. But preserve existing data that wasn't created by this seeder
    }
}
