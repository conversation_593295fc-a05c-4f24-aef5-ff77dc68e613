<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\Student;
use App\Models\Semester;
use App\Models\Enrollment;
use App\Models\CurriculumVersion;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * EnrollmentProcessor - CRITICAL FIRST PHASE
 * 
 * Creates semester enrollments for all active students BEFORE any course offerings.
 * This establishes the enrollment demand foundation that drives course offering creation.
 * 
 * Requirements:
 * - Must run BEFORE course offering creation
 * - Calculate correct semester_number for each student using admission_date vs semester dates
 * - Link enrollments to curriculum_version_id and semester_id
 * - Set enrollment status as 'in_progress' for current semester
 */
class EnrollmentProcessor extends AbstractPhaseProcessor
{
    public function __construct()
    {
        parent::__construct('enrollments');
    }

    /**
     * Process the enrollment creation phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting enrollment creation phase - CRITICAL FOUNDATION');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $semester = $this->getSemester($semesterCode);
            if (!$semester) {
                throw new PhaseExecutionException(
                    "Semester not found: {$semesterCode}",
                    0,
                    null,
                    $this->phaseName
                );
            }

            $this->createSemesterEnrollments($semester);
        }

        Log::info('Enrollment creation phase completed - Foundation established');
        return true;
    }

    /**
     * Create semester enrollments for all active students
     * Requirements: Create enrollments BEFORE course offerings to establish demand
     */
    public function createSemesterEnrollments(Semester $semester): Collection
    {
        Log::info("Creating semester enrollments for: {$semester->code}");

        // Get all eligible students for enrollment
        $eligibleStudents = $this->determineEligibleStudents($semester);
        
        if ($eligibleStudents->isEmpty()) {
            Log::warning("No eligible students found for semester {$semester->code}");
            return collect();
        }

        $createdEnrollments = collect();
        $enrollmentCount = 0;

        foreach ($eligibleStudents as $student) {
            try {
                // Calculate student's semester number for this semester
                $semesterNumber = $this->calculateStudentSemesterNumber($student, $semester);
                
                // Create enrollment if it doesn't exist
                $enrollment = $this->createEnrollment($student, $semester, $semesterNumber);
                
                if ($enrollment && $enrollment->wasRecentlyCreated) {
                    $createdEnrollments->push($enrollment);
                    $enrollmentCount++;
                }
            } catch (\Exception $e) {
                Log::warning("Failed to create enrollment for student {$student->student_id}: " . $e->getMessage());
                continue;
            }
        }

        $this->addRecordCount('enrollments', $enrollmentCount);
        
        Log::info("Created {$enrollmentCount} enrollments for semester {$semester->code}");
        
        return $createdEnrollments;
    }

    /**
     * Calculate student's semester number based on admission date vs semester dates
     * Requirements: Calculate correct semester_number using admission_date vs semester start dates
     */
    public function calculateStudentSemesterNumber(Student $student, Semester $semester): int
    {
        $admissionDate = Carbon::parse($student->admission_date);
        $semesterStartDate = Carbon::parse($semester->start_date);

        // Get all semesters from admission date up to and including the target semester
        $semestersSinceAdmission = Semester::where('start_date', '>=', $admissionDate)
            ->where('start_date', '<=', $semesterStartDate)
            ->orderBy('start_date', 'asc')
            ->get();

        // The semester number is the count of semesters since admission
        $semesterNumber = $semestersSinceAdmission->count();

        // Ensure minimum semester number is 1
        $calculatedSemester = max(1, $semesterNumber);

        Log::debug("Student {$student->student_id} semester calculation", [
            'admission_date' => $admissionDate->format('Y-m-d'),
            'semester_start' => $semesterStartDate->format('Y-m-d'),
            'semesters_since_admission' => $semestersSinceAdmission->count(),
            'calculated_semester_number' => $calculatedSemester,
        ]);

        return $calculatedSemester;
    }

    /**
     * Determine eligible students for enrollment in a semester
     * Requirements: Only enroll active students who haven't already been enrolled
     */
    public function determineEligibleStudents(Semester $semester): Collection
    {
        // Get active students with curriculum versions
        $activeStudents = Student::where('academic_status', 'active')
            ->whereNotNull('curriculum_version_id')
            ->whereNotNull('admission_date')
            ->with(['curriculumVersion'])
            ->get();

        // Filter out students who are already enrolled for this semester
        $eligibleStudents = $activeStudents->filter(function ($student) use ($semester) {
            $existingEnrollment = Enrollment::where('student_id', $student->id)
                ->where('semester_id', $semester->id)
                ->exists();
            
            return !$existingEnrollment;
        });

        // Filter students who should be enrolled in this semester
        // (admission date should be before or equal to semester start)
        $eligibleStudents = $eligibleStudents->filter(function ($student) use ($semester) {
            $admissionDate = Carbon::parse($student->admission_date);
            $semesterStart = Carbon::parse($semester->start_date);
            
            return $admissionDate->lte($semesterStart);
        });

        Log::info("Eligible students for enrollment", [
            'semester' => $semester->code,
            'total_active_students' => $activeStudents->count(),
            'eligible_students' => $eligibleStudents->count(),
        ]);

        return $eligibleStudents;
    }

    /**
     * Create enrollment record for a student
     * Requirements: Link to curriculum_version_id and semester_id, set status as 'in_progress'
     */
    public function createEnrollment(Student $student, Semester $semester, int $semesterNumber): Enrollment
    {
        // Check if enrollment already exists
        $existingEnrollment = Enrollment::where('student_id', $student->id)
            ->where('semester_id', $semester->id)
            ->first();

        if ($existingEnrollment) {
            Log::debug("Enrollment already exists for student {$student->student_id} in semester {$semester->code}");
            return $existingEnrollment;
        }

        // Create new enrollment
        $enrollment = Enrollment::create([
            'student_id' => $student->id,
            'semester_id' => $semester->id,
            'curriculum_version_id' => $student->curriculum_version_id,
            'semester_number' => $semesterNumber,
            'status' => 'in_progress',
            'notes' => 'Created by Academic Lifecycle Seeder - Enrollment Processor',
        ]);

        Log::debug("Created enrollment", [
            'student_id' => $student->student_id,
            'semester' => $semester->code,
            'semester_number' => $semesterNumber,
            'curriculum_version_id' => $student->curriculum_version_id,
        ]);

        return $enrollment;
    }

    /**
     * Get semester by code
     */
    private function getSemester(string $semesterCode): ?Semester
    {
        return Semester::where('code', $semesterCode)->first();
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have active students
        $activeStudentsCount = Student::where('academic_status', 'active')
            ->whereNotNull('curriculum_version_id')
            ->whereNotNull('admission_date')
            ->count();

        if ($activeStudentsCount === 0) {
            Log::warning('No active students with curriculum versions and admission dates found');
            return false;
        }

        // Check if we have semesters
        $semestersCount = Semester::count();
        if ($semestersCount === 0) {
            Log::warning('No semesters found for enrollment creation');
            return false;
        }

        Log::info("Prerequisites validated", [
            'active_students' => $activeStudentsCount,
            'semesters' => $semestersCount,
        ]);

        return true;
    }

    /**
     * Rollback the enrollment creation phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back enrollment creation phase");
        
        // In a real implementation, you would:
        // 1. Delete enrollments created by this seeder (marked with specific notes)
        // 2. Preserve manually created enrollments
        // 3. Update related records if necessary
        
        // For now, we log the rollback request
        Log::warning("Enrollment rollback requested - manual cleanup may be required");
    }
}
