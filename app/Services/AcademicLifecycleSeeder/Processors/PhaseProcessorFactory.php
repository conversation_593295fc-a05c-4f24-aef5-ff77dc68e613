<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Services\AcademicLifecycleSeeder\Exceptions\ConfigurationException;

class PhaseProcessorFactory
{
    /**
     * Create a phase processor instance
     */
    public static function create(string $phase): PhaseProcessorInterface
    {
        return match ($phase) {
            'validation' => new ValidationPhaseProcessor(),
            'enrollments' => new EnrollmentProcessor(),
            'course_offerings' => new CourseOfferingProcessor(),
            'student_registration' => new StudentRegistrationProcessor(),
            'group_assignment' => new GroupAssignmentProcessor(),
            'class_sessions' => new ClassSessionProcessor(),
            'assessment_grading' => new AssessmentProcessor(),
            'gpa_standing' => new GpaStandingProcessor(),
            'special_cases' => throw new ConfigurationException('Special cases processor not implemented yet'),
            default => throw new ConfigurationException("Unknown phase processor: {$phase}"),
        };
    }

    /**
     * Get available phase processors
     */
    public static function getAvailableProcessors(): array
    {
        return [
            'validation' => 'Validate Prerequisites',
            'enrollments' => 'Create Student Semester Enrollments',
            'course_offerings' => 'Generate Course Offerings',
            'student_registration' => 'Register Students for Courses',
            'group_assignment' => 'Assign Students to Sections',
            'class_sessions' => 'Generate Class Sessions',
            'assessment_grading' => 'Create Assessments and Grades',
            'gpa_standing' => 'Calculate GPA and Academic Standing',
            'special_cases' => 'Handle Special Cases and Retakes',
        ];
    }
}
