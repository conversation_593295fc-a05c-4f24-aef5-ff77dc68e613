<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Services\AcademicLifecycleSeeder\Exceptions\ConfigurationException;

class PhaseProcessorFactory
{
    /**
     * Create a phase processor instance
     */
    public static function create(string $phase): PhaseProcessorInterface
    {
        return match ($phase) {
            'validation' => new ValidationPhaseProcessor(),
            'course_offerings' => throw new ConfigurationException('Course offerings processor not implemented yet'),
            'student_registration' => throw new ConfigurationException('Student registration processor not implemented yet'),
            'group_assignment' => throw new ConfigurationException('Group assignment processor not implemented yet'),
            'class_sessions' => throw new ConfigurationException('Class sessions processor not implemented yet'),
            'assessment_grading' => throw new ConfigurationException('Assessment grading processor not implemented yet'),
            'gpa_standing' => throw new ConfigurationException('GPA standing processor not implemented yet'),
            'special_cases' => throw new ConfigurationException('Special cases processor not implemented yet'),
            default => throw new ConfigurationException("Unknown phase processor: {$phase}"),
        };
    }

    /**
     * Get available phase processors
     */
    public static function getAvailableProcessors(): array
    {
        return [
            'validation' => 'Validate Prerequisites',
            'course_offerings' => 'Generate Course Offerings',
            'student_registration' => 'Register Students for Courses',
            'group_assignment' => 'Assign Students to Sections',
            'class_sessions' => 'Generate Class Sessions',
            'assessment_grading' => 'Create Assessments and Grades',
            'gpa_standing' => 'Calculate GPA and Academic Standing',
            'special_cases' => 'Handle Special Cases and Retakes',
        ];
    }
}
