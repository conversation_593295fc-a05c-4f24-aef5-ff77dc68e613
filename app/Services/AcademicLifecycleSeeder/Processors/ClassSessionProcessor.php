<?php

declare(strict_types=1);

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Models\CourseOffering;
use App\Models\ClassSession;
use App\Models\Attendance;
use App\Models\CourseRegistration;
use App\Models\Syllabus;
use App\Models\Room;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use App\Services\AcademicLifecycleSeeder\Generators\RealisticDataGenerator;
use App\Services\AcademicLifecycleSeeder\Exceptions\PhaseExecutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * ClassSessionProcessor
 * 
 * Generates class sessions and attendance records for course offerings.
 * 
 * Requirements:
 * - Calculate number of sessions using syllabus total_hours / hours_per_session
 * - Generate class sessions with appropriate session types (lecture, lab, tutorial)
 * - Assign room_id, session_date, start_time, end_time for each session
 * - Create attendance records for all registered students per session
 * - Apply realistic attendance patterns based on student profiles
 */
class ClassSessionProcessor extends AbstractPhaseProcessor
{
    private RealisticDataGenerator $dataGenerator;

    public function __construct()
    {
        parent::__construct('class_sessions');
        $this->dataGenerator = new RealisticDataGenerator();
    }

    /**
     * Process the class session generation phase
     */
    protected function process(SeederConfiguration $configuration): bool
    {
        Log::info('Starting class session generation phase');

        foreach ($configuration->targetSemesters as $semesterCode) {
            $this->processSemesterClassSessions($semesterCode, $configuration);
        }

        Log::info('Class session generation phase completed successfully');
        return true;
    }

    /**
     * Process class sessions for a specific semester
     */
    private function processSemesterClassSessions(string $semesterCode, SeederConfiguration $configuration): void
    {
        // Get course offerings for this semester
        $courseOfferings = CourseOffering::whereHas('semester', function ($query) use ($semesterCode) {
            $query->where('code', $semesterCode);
        })
        ->where('is_active', true)
        ->with(['semester', 'curriculumUnit.unit', 'syllabus'])
        ->get();

        if ($courseOfferings->isEmpty()) {
            Log::warning("No course offerings found for semester {$semesterCode}");
            return;
        }

        $totalSessions = 0;
        $totalAttendanceRecords = 0;

        foreach ($courseOfferings as $courseOffering) {
            $result = $this->generateClassSessionsForCourse($courseOffering, $configuration);
            $totalSessions += $result['sessions_created'];
            $totalAttendanceRecords += $result['attendance_records_created'];
        }

        $this->addRecordCount('class_sessions', $totalSessions);
        $this->addRecordCount('attendance_records', $totalAttendanceRecords);

        Log::info("Class sessions completed for semester {$semesterCode}", [
            'sessions_created' => $totalSessions,
            'attendance_records_created' => $totalAttendanceRecords,
        ]);
    }

    /**
     * Generate class sessions for a course offering
     * Requirements: 3.1 - Calculate number of sessions using syllabus total_hours / hours_per_session
     */
    public function generateClassSessionsForCourse(CourseOffering $courseOffering, SeederConfiguration $configuration): array
    {
        $syllabus = $courseOffering->syllabus;
        
        if (!$syllabus) {
            Log::warning("No syllabus found for course offering {$courseOffering->id}");
            return ['sessions_created' => 0, 'attendance_records_created' => 0];
        }

        // Calculate number of sessions
        $totalHours = $syllabus->total_hours ?? 48; // Default 48 hours
        $hoursPerSession = $syllabus->hours_per_session ?? 2; // Default 2 hours per session
        $numberOfSessions = (int) ceil($totalHours / $hoursPerSession);

        Log::info("Generating class sessions", [
            'course' => $courseOffering->curriculumUnit->unit->code,
            'total_hours' => $totalHours,
            'hours_per_session' => $hoursPerSession,
            'number_of_sessions' => $numberOfSessions,
        ]);

        // Generate class sessions
        $sessions = $this->createClassSessions($courseOffering, $numberOfSessions, $hoursPerSession);
        
        // Generate attendance records for all sessions
        $attendanceRecords = $this->generateAttendanceRecords($sessions, $courseOffering);

        return [
            'sessions_created' => $sessions->count(),
            'attendance_records_created' => $attendanceRecords,
        ];
    }

    /**
     * Create class sessions with appropriate session types and scheduling
     * Requirements: 3.2 - Assign room_id, session_date, start_time, end_time for each session
     */
    public function createClassSessions(CourseOffering $courseOffering, int $numberOfSessions, int $hoursPerSession): Collection
    {
        $sessions = collect();
        $semester = $courseOffering->semester;
        
        // Get semester date range
        $semesterStart = Carbon::parse($semester->start_date);
        $semesterEnd = Carbon::parse($semester->end_date);
        
        // Calculate weekly schedule based on course offering schedule
        $scheduleDays = $courseOffering->schedule_days ?? ['Monday', 'Wednesday'];
        $startTime = $courseOffering->schedule_time_start ? $courseOffering->schedule_time_start->format('H:i:s') : '09:00:00';
        $endTime = $courseOffering->schedule_time_end ? $courseOffering->schedule_time_end->format('H:i:s') : '11:00:00';

        // Generate sessions across the semester
        $currentDate = $semesterStart->copy();
        $sessionCount = 0;
        $weekNumber = 1;

        while ($sessionCount < $numberOfSessions && $currentDate->lte($semesterEnd)) {
            foreach ($scheduleDays as $dayName) {
                if ($sessionCount >= $numberOfSessions) {
                    break;
                }

                // Find the next occurrence of this day
                $sessionDate = $this->getNextDayOfWeek($currentDate, $dayName);
                
                if ($sessionDate->gt($semesterEnd)) {
                    break;
                }

                // Create class session
                $session = $this->createClassSession(
                    $courseOffering,
                    $sessionDate,
                    $startTime,
                    $endTime,
                    $sessionCount + 1,
                    $weekNumber
                );

                $sessions->push($session);
                $sessionCount++;
            }
            
            $currentDate->addWeek();
            $weekNumber++;
        }

        Log::info("Created {$sessions->count()} class sessions for {$courseOffering->curriculumUnit->unit->code}");
        
        return $sessions;
    }

    /**
     * Create a single class session
     */
    private function createClassSession(
        CourseOffering $courseOffering,
        Carbon $sessionDate,
        string $startTime,
        string $endTime,
        int $sessionNumber,
        int $weekNumber
    ): ClassSession {
        // Determine session type based on session number and course type
        $sessionType = $this->determineSessionType($sessionNumber, $courseOffering);
        
        // Assign room
        $room = $this->assignRoom($courseOffering);

        return ClassSession::create([
            'course_offering_id' => $courseOffering->id,
            'session_type' => $sessionType,
            'session_date' => $sessionDate->format('Y-m-d'),
            'start_time' => $startTime,
            'end_time' => $endTime,
            'room_id' => $room?->id,
            'status' => 'scheduled',
            'attendance_required' => true,
            'attendance_tracking_enabled' => true,
            'delivery_mode' => $courseOffering->delivery_mode,
            'session_title' => "Session {$sessionNumber}",
            'student_instructions' => "Generated by Academic Lifecycle Seeder",
        ]);
    }

    /**
     * Determine session type based on session number and course characteristics
     */
    private function determineSessionType(int $sessionNumber, CourseOffering $courseOffering): string
    {
        // Simple logic: first session is lecture, then alternate between lecture and tutorial
        if ($sessionNumber === 1) {
            return 'lecture';
        }
        
        // Check if this is a practical/lab course
        $unitType = $courseOffering->curriculumUnit->type ?? 'theory';
        
        if ($unitType === 'practical' || $unitType === 'lab') {
            return ($sessionNumber % 3 === 0) ? 'laboratory' : 'lecture';
        }
        
        // For theory courses, alternate between lecture and tutorial
        return ($sessionNumber % 2 === 0) ? 'tutorial' : 'lecture';
    }

    /**
     * Assign a room for the class session
     */
    private function assignRoom(CourseOffering $courseOffering): ?Room
    {
        // For online courses, no room needed
        if ($courseOffering->delivery_mode === 'online') {
            return null;
        }

        // Get available rooms
        $rooms = Room::take(20)->get();
        
        if ($rooms->isEmpty()) {
            return null;
        }

        return $rooms->random();
    }

    /**
     * Get the next occurrence of a specific day of the week
     */
    private function getNextDayOfWeek(Carbon $date, string $dayName): Carbon
    {
        $dayNumber = [
            'Sunday' => 0, 'Monday' => 1, 'Tuesday' => 2, 'Wednesday' => 3,
            'Thursday' => 4, 'Friday' => 5, 'Saturday' => 6
        ][$dayName] ?? 1;

        $nextDate = $date->copy();
        
        while ($nextDate->dayOfWeek !== $dayNumber) {
            $nextDate->addDay();
        }

        return $nextDate;
    }

    /**
     * Generate attendance records for class sessions
     * Requirements: 3.3 - Create attendance records with randomized attendance status
     */
    public function generateAttendanceRecords(Collection $sessions, CourseOffering $courseOffering): int
    {
        // Get all students registered for this course
        $registrations = CourseRegistration::where('course_offering_id', $courseOffering->id)
            ->with('student')
            ->get();

        if ($registrations->isEmpty()) {
            Log::warning("No registrations found for course offering {$courseOffering->id}");
            return 0;
        }

        $totalAttendanceRecords = 0;

        foreach ($sessions as $session) {
            foreach ($registrations as $registration) {
                $this->createAttendanceRecord($session, $registration);
                $totalAttendanceRecords++;
            }
        }

        Log::info("Created {$totalAttendanceRecords} attendance records for {$courseOffering->curriculumUnit->unit->code}");
        
        return $totalAttendanceRecords;
    }

    /**
     * Create an attendance record for a student and session
     */
    private function createAttendanceRecord(ClassSession $session, CourseRegistration $registration): Attendance
    {
        // Generate realistic attendance status
        $attendanceStatus = $this->dataGenerator->generateAttendanceStatus($registration->student, $session);

        return Attendance::create([
            'class_session_id' => $session->id,
            'student_id' => $registration->student_id,
            'attendance_status' => $attendanceStatus,
            'check_in_time' => $attendanceStatus === 'present' ? $session->start_time : null,
            'check_out_time' => $attendanceStatus === 'present' ? $session->end_time : null,
            'notes' => 'Generated by Academic Lifecycle Seeder',
        ]);
    }

    /**
     * Validate prerequisites for this phase
     */
    public function validatePrerequisites(): bool
    {
        // Check if we have course offerings
        $offeringsCount = CourseOffering::where('is_active', true)->count();
        if ($offeringsCount === 0) {
            Log::warning('No active course offerings found for class session generation');
            return false;
        }

        // Check if we have syllabi
        $syllabiCount = Syllabus::where('is_active', true)->count();
        if ($syllabiCount === 0) {
            Log::warning('No active syllabi found for class session generation');
            return false;
        }

        Log::info("Class session prerequisites validated", [
            'course_offerings' => $offeringsCount,
            'syllabi' => $syllabiCount,
        ]);

        return true;
    }

    /**
     * Rollback the class session generation phase
     */
    public function rollback(): void
    {
        Log::info("Rolling back class session generation phase");
        
        // In a real implementation, you would:
        // 1. Delete class sessions created by this seeder
        // 2. Delete associated attendance records
        // 3. Preserve manually created sessions
        
        Log::warning("Class session rollback requested - manual cleanup may be required");
    }
}
