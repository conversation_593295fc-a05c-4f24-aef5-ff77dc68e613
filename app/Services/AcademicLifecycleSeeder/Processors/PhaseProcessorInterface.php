<?php

namespace App\Services\AcademicLifecycleSeeder\Processors;

use App\Services\AcademicLifecycleSeeder\PhaseResult;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;

interface PhaseProcessorInterface
{
    /**
     * Execute the phase processor
     */
    public function execute(SeederConfiguration $configuration): PhaseResult;

    /**
     * Rollback the phase execution
     */
    public function rollback(): void;

    /**
     * Get the phase name
     */
    public function getPhaseName(): string;

    /**
     * Check if the phase can be executed
     */
    public function canExecute(): bool;

    /**
     * Validate prerequisites for the phase
     */
    public function validatePrerequisites(): bool;
}
