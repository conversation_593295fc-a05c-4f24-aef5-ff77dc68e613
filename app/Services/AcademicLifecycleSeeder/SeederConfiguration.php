<?php

namespace App\Services\AcademicLifecycleSeeder;

use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class SeederConfiguration
{
    public array $targetSemesters;
    public int $maxStudentsPerSection;
    public array $gradeDistribution;
    public array $attendancePatterns;
    public bool $includeRetakes;
    public bool $includeProgramChanges;
    public bool $includeAcademicHolds;
    public int $batchSize;
    public bool $enableProgressReporting;
    public array $specialCases;
    public array $performance;

    public function __construct(array $config = [])
    {
        $this->validateConfiguration($config);
        $this->loadConfiguration($config);
    }

    /**
     * Validate the configuration array
     */
    private function validateConfiguration(array $config): void
    {
        $validator = Validator::make($config, [
            'target_semesters' => 'required|array|min:1',
            'target_semesters.*' => 'required|string',
            'max_students_per_section' => 'integer|min:10|max:100',
            'grade_distribution' => 'array',
            'grade_distribution.A' => 'integer|min:0|max:100',
            'grade_distribution.B' => 'integer|min:0|max:100',
            'grade_distribution.C' => 'integer|min:0|max:100',
            'grade_distribution.D' => 'integer|min:0|max:100',
            'grade_distribution.F' => 'integer|min:0|max:100',
            'attendance_patterns' => 'array',
            'attendance_patterns.excellent' => 'integer|min:0|max:100',
            'attendance_patterns.good' => 'integer|min:0|max:100',
            'attendance_patterns.average' => 'integer|min:0|max:100',
            'attendance_patterns.poor' => 'integer|min:0|max:100',
            'include_retakes' => 'boolean',
            'include_program_changes' => 'boolean',
            'include_academic_holds' => 'boolean',
            'batch_size' => 'integer|min:10|max:1000',
            'enable_progress_reporting' => 'boolean',
            'special_cases' => 'array',
            'special_cases.retake_percentage' => 'integer|min:0|max:50',
            'special_cases.program_change_percentage' => 'integer|min:0|max:20',
            'special_cases.academic_hold_percentage' => 'integer|min:0|max:30',
            'performance' => 'array',
            'performance.batch_size' => 'integer|min:10|max:1000',
            'performance.enable_progress_reporting' => 'boolean',
            'performance.parallel_processing' => 'boolean',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        // Validate grade distribution sums to 100
        $gradeDistribution = $config['grade_distribution'] ?? $this->getDefaultGradeDistribution();
        $gradeSum = array_sum($gradeDistribution);
        if ($gradeSum !== 100) {
            throw new ValidationException(
                Validator::make([], []),
                ['grade_distribution' => 'Grade distribution must sum to 100%']
            );
        }

        // Validate attendance patterns sum to 100
        $attendancePatterns = $config['attendance_patterns'] ?? $this->getDefaultAttendancePatterns();
        $attendanceSum = array_sum($attendancePatterns);
        if ($attendanceSum !== 100) {
            throw new ValidationException(
                Validator::make([], []),
                ['attendance_patterns' => 'Attendance patterns must sum to 100%']
            );
        }
    }

    /**
     * Load configuration with defaults
     */
    private function loadConfiguration(array $config): void
    {
        $this->targetSemesters = $config['target_semesters'];
        $this->maxStudentsPerSection = $config['max_students_per_section'] ?? 55;
        $this->gradeDistribution = $config['grade_distribution'] ?? $this->getDefaultGradeDistribution();
        $this->attendancePatterns = $config['attendance_patterns'] ?? $this->getDefaultAttendancePatterns();
        $this->includeRetakes = $config['include_retakes'] ?? true;
        $this->includeProgramChanges = $config['include_program_changes'] ?? true;
        $this->includeAcademicHolds = $config['include_academic_holds'] ?? true;
        $this->batchSize = $config['batch_size'] ?? 100;
        $this->enableProgressReporting = $config['enable_progress_reporting'] ?? true;

        $this->specialCases = array_merge([
            'retake_percentage' => 10,
            'program_change_percentage' => 5,
            'academic_hold_percentage' => 3,
        ], $config['special_cases'] ?? []);

        $this->performance = array_merge([
            'batch_size' => 100,
            'enable_progress_reporting' => true,
            'parallel_processing' => false,
        ], $config['performance'] ?? []);
    }

    /**
     * Get default grade distribution
     */
    private function getDefaultGradeDistribution(): array
    {
        return [
            'A' => 15,
            'B' => 35,
            'C' => 35,
            'D' => 10,
            'F' => 5,
        ];
    }

    /**
     * Get default attendance patterns
     */
    private function getDefaultAttendancePatterns(): array
    {
        return [
            'excellent' => 20,
            'good' => 50,
            'average' => 25,
            'poor' => 5,
        ];
    }

    /**
     * Convert configuration to array
     */
    public function toArray(): array
    {
        return [
            'target_semesters' => $this->targetSemesters,
            'max_students_per_section' => $this->maxStudentsPerSection,
            'grade_distribution' => $this->gradeDistribution,
            'attendance_patterns' => $this->attendancePatterns,
            'include_retakes' => $this->includeRetakes,
            'include_program_changes' => $this->includeProgramChanges,
            'include_academic_holds' => $this->includeAcademicHolds,
            'batch_size' => $this->batchSize,
            'enable_progress_reporting' => $this->enableProgressReporting,
            'special_cases' => $this->specialCases,
            'performance' => $this->performance,
        ];
    }

    /**
     * Create configuration from file
     */
    public static function fromFile(string $configPath): self
    {
        if (!file_exists($configPath)) {
            throw new \InvalidArgumentException("Configuration file not found: {$configPath}");
        }

        $config = require $configPath;

        if (!is_array($config)) {
            throw new \InvalidArgumentException("Configuration file must return an array");
        }

        return new self($config);
    }

    /**
     * Get default configuration
     */
    public static function getDefault(): self
    {
        return new self([
            'target_semesters' => ['2024-1'],
            'max_students_per_section' => 55,
            'grade_distribution' => [
                'A' => 15,
                'B' => 35,
                'C' => 35,
                'D' => 10,
                'F' => 5,
            ],
            'attendance_patterns' => [
                'excellent' => 20,
                'good' => 50,
                'average' => 25,
                'poor' => 5,
            ],
            'include_retakes' => true,
            'include_program_changes' => true,
            'include_academic_holds' => true,
            'batch_size' => 100,
            'enable_progress_reporting' => true,
            'special_cases' => [
                'retake_percentage' => 10,
                'program_change_percentage' => 5,
                'academic_hold_percentage' => 3,
            ],
            'performance' => [
                'batch_size' => 100,
                'enable_progress_reporting' => true,
                'parallel_processing' => false,
            ],
        ]);
    }
}
