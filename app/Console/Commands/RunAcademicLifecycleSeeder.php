<?php

namespace App\Console\Commands;

use App\Services\AcademicLifecycleSeeder\AcademicLifecycleSeeder;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;

class RunAcademicLifecycleSeeder extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'seeder:academic-lifecycle
                            {--config= : Path to custom configuration file}
                            {--semesters=* : Target semester codes}
                            {--max-students=55 : Maximum students per section}
                            {--dry-run : Run without making database changes}
                            {--progress : Show progress during execution}
                            {--environment= : Use environment-specific configuration}';

    /**
     * The console command description.
     */
    protected $description = 'Generate comprehensive academic lifecycle data for testing and development';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🎓 Academic Lifecycle Seeder');
        $this->info('================================');

        try {
            // Load configuration
            $config = $this->loadConfiguration();

            if ($this->option('dry-run')) {
                $this->info('🔍 DRY RUN MODE - No database changes will be made');
                $this->displayConfiguration($config);
                return self::SUCCESS;
            }

            // Confirm execution
            if (!$this->confirmExecution($config)) {
                $this->info('❌ Seeder execution cancelled');
                return self::SUCCESS;
            }

            // Execute seeder
            $seeder = new AcademicLifecycleSeeder();

            if ($this->option('progress')) {
                return $this->executeWithProgress($seeder, $config);
            } else {
                return $this->executeWithoutProgress($seeder, $config);
            }
        } catch (\Exception $e) {
            $this->error('❌ Seeder failed: ' . $e->getMessage());
            $this->error('Exception type: ' . get_class($e));
            $this->error('Exception trace:');
            $this->error($e->getTraceAsString());

            return self::FAILURE;
        }
    }

    /**
     * Load seeder configuration
     */
    private function loadConfiguration(): array
    {
        $config = [];

        // Load from custom config file if provided
        if ($configPath = $this->option('config')) {
            if (!file_exists($configPath)) {
                throw new \InvalidArgumentException("Configuration file not found: {$configPath}");
            }
            $config = require $configPath;
        }

        // Load environment-specific configuration
        if ($environment = $this->option('environment')) {
            $envConfig = Config::get("academic-lifecycle-seeder.environments.{$environment}");
            if ($envConfig) {
                $config = array_merge($config, $envConfig);
            } else {
                $this->warn("Environment configuration not found: {$environment}");
            }
        }

        // Override with command line options
        if ($semesters = $this->option('semesters')) {
            $config['target_semesters'] = $semesters;
        }

        if ($maxStudents = $this->option('max-students')) {
            $config['max_students_per_section'] = (int) $maxStudents;
        }

        // Use default configuration as base
        $defaultConfig = Config::get('academic-lifecycle-seeder', []);
        $config = array_merge($defaultConfig, $config);

        return $config;
    }

    /**
     * Display configuration for review
     */
    private function displayConfiguration(array $config): void
    {
        $this->info('📋 Configuration:');
        $this->table(
            ['Setting', 'Value'],
            [
                ['Target Semesters', implode(', ', $config['target_semesters'] ?? [])],
                ['Max Students per Section', $config['max_students_per_section'] ?? 'N/A'],
                ['Include Retakes', ($config['include_retakes'] ?? false) ? 'Yes' : 'No'],
                ['Include Program Changes', ($config['include_program_changes'] ?? false) ? 'Yes' : 'No'],
                ['Include Academic Holds', ($config['include_academic_holds'] ?? false) ? 'Yes' : 'No'],
                ['Batch Size', $config['performance']['batch_size'] ?? 'N/A'],
            ]
        );
    }

    /**
     * Confirm execution with user
     */
    private function confirmExecution(array $config): bool
    {
        $this->displayConfiguration($config);

        $this->warn('⚠️  This will generate academic data for the specified semesters.');
        $this->warn('   Existing data may be affected.');

        return $this->confirm('Do you want to continue?', false);
    }

    /**
     * Execute seeder with progress reporting
     */
    private function executeWithProgress(AcademicLifecycleSeeder $seeder, array $config): int
    {
        $this->info('🚀 Starting seeder execution...');

        // Start seeder in background and monitor progress
        $configuration = new SeederConfiguration($config);
        $seeder->setConfiguration($configuration);

        $progressBar = $this->output->createProgressBar(count($seeder->getPhases()));
        $progressBar->setFormat('verbose');

        // Execute seeder
        $result = $seeder->execute($config);

        $progressBar->finish();
        $this->newLine(2);

        if ($result->success) {
            $this->displaySuccessResults($result);
            return self::SUCCESS;
        } else {
            $this->displayFailureResults($result);
            return self::FAILURE;
        }
    }

    /**
     * Execute seeder without progress reporting
     */
    private function executeWithoutProgress(AcademicLifecycleSeeder $seeder, array $config): int
    {
        $this->info('🚀 Starting seeder execution...');

        $result = $seeder->execute($config);

        if ($result->success) {
            $this->displaySuccessResults($result);
            return self::SUCCESS;
        } else {
            $this->displayFailureResults($result);
            return self::FAILURE;
        }
    }

    /**
     * Display success results
     */
    private function displaySuccessResults($result): void
    {
        $this->info('✅ Seeder completed successfully!');
        $this->newLine();

        $summary = $result->progressReport->getSummary();

        $this->info('📊 Summary:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Execution Time', $summary['execution_time'] . ' seconds'],
                ['Phases Completed', $summary['phases_completed'] . '/' . $summary['total_phases']],
                ['Records Processed', number_format($summary['records_processed'])],
            ]
        );

        if (!empty($result->progressReport->recordCounts)) {
            $this->info('📈 Records Created:');
            $recordData = [];
            foreach ($result->progressReport->recordCounts as $type => $count) {
                $recordData[] = [ucwords(str_replace('_', ' ', $type)), number_format($count)];
            }
            $this->table(['Type', 'Count'], $recordData);
        }

        if ($result->hasWarnings()) {
            $this->warn('⚠️  Warnings:');
            foreach ($result->warnings as $warning) {
                $this->warn("  • {$warning}");
            }
        }
    }

    /**
     * Display failure results
     */
    private function displayFailureResults($result): void
    {
        $this->error('❌ Seeder failed!');
        $this->newLine();

        if ($result->errorMessage) {
            $this->error('Error: ' . $result->errorMessage);
        }

        $summary = $result->progressReport->getSummary();

        $this->info('📊 Execution Summary:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Execution Time', $summary['execution_time'] . ' seconds'],
                ['Phases Completed', $summary['phases_completed'] . '/' . $summary['total_phases']],
                ['Current Phase', $summary['current_phase']],
                ['Records Processed', number_format($summary['records_processed'])],
            ]
        );
    }
}
