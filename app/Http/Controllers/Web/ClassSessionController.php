<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\ClassSession;
use App\Services\ClassSessionService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ClassSessionController extends Controller
{
    public function __construct(protected ClassSessionService $classSessionService) {}

    /**
     * Display a listing of class sessions
     */
    public function index(Request $request): Response
    {
        $filters = $request->only(['search', 'status', 'session_type', 'delivery_mode', 'date_from', 'date_to']);
        $perPage = $request->get('per_page', 15);

        $sessions = $this->classSessionService->getPaginatedSessions($filters, $perPage);

        // Add attendance stats to each session
        $sessions->getCollection()->transform(function ($session) {
            $session->attendance_stats = $session->attendanceStats;

            return $session;
        });

        // Get filter options
        $statusOptions = [
            'scheduled' => 'Scheduled',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
        ];

        $sessionTypeOptions = [
            'lecture' => 'Lecture',
            'tutorial' => 'Tutorial',
            'practical' => 'Practical',
            'workshop' => 'Workshop',
            'seminar' => 'Seminar',
            'exam' => 'Exam',
        ];

        $deliveryModeOptions = [
            'in_person' => 'In Person',
            'online' => 'Online',
            'hybrid' => 'Hybrid',
        ];

        return Inertia::render('class-sessions/Index', [
            'sessions' => $sessions,
            'filters' => $request->only(['search', 'status', 'session_type', 'delivery_mode', 'date_from', 'date_to', 'per_page']),
            'statusOptions' => $statusOptions,
            'sessionTypeOptions' => $sessionTypeOptions,
            'deliveryModeOptions' => $deliveryModeOptions,
        ]);
    }

    /**
     * Show the form for creating a new class session
     */
    public function create(): Response
    {
        $courseOfferings = $this->classSessionService->getCourseOfferingsForSelect();

        return Inertia::render('class-sessions/Create', [
            'courseOfferings' => $courseOfferings,
        ]);
    }

    /**
     * Store a newly created class session
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'course_offering_id' => 'required|exists:course_offerings,id',
            'session_title' => 'required|string|max:255',
            'session_description' => 'nullable|string',
            'session_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'session_type' => 'required|in:lecture,tutorial,practical,workshop,seminar,exam',
            'delivery_mode' => 'required|in:in_person,online,hybrid',
            'status' => 'required|in:scheduled,in_progress,completed,cancelled',
            'attendance_required' => 'boolean',
            'attendance_tracking_enabled' => 'boolean',
            'learning_objectives' => 'nullable|array',
            'required_materials' => 'nullable|array',
            'topics_covered' => 'nullable|array',
            'online_meeting_url' => 'nullable|url',
            'instructor_notes' => 'nullable|string',
        ]);

        $classSession = $this->classSessionService->createClassSession($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Class session created successfully',
                'data' => $classSession,
            ]);
        }

        return redirect()->route('class-sessions.index')
            ->with('success', 'Class session created successfully');
    }

    /**
     * Display the specified class session
     */
    public function show(ClassSession $classSession): Response
    {
        $session = $this->classSessionService->getSessionWithRelations($classSession->id);
        $session->attendance_stats = $session->attendanceStats;

        return Inertia::render('class-sessions/Show', [
            'session' => $session,
        ]);
    }

    /**
     * Show the form for editing the specified class session
     */
    public function edit(ClassSession $classSession): Response
    {
        $courseOfferings = $this->classSessionService->getCourseOfferingsForSelect();

        return Inertia::render('class-sessions/Edit', [
            'session' => $classSession,
            'courseOfferings' => $courseOfferings,
        ]);
    }

    /**
     * Update the specified class session
     */
    public function update(Request $request, ClassSession $classSession)
    {
        $validated = $request->validate([
            'course_offering_id' => 'required|exists:course_offerings,id',
            'session_title' => 'required|string|max:255',
            'session_description' => 'nullable|string',
            'session_date' => 'required|date',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'session_type' => 'required|in:lecture,tutorial,practical,workshop,seminar,exam',
            'delivery_mode' => 'required|in:in_person,online,hybrid',
            'status' => 'required|in:scheduled,in_progress,completed,cancelled',
            'attendance_required' => 'boolean',
            'attendance_tracking_enabled' => 'boolean',
            'learning_objectives' => 'nullable|array',
            'required_materials' => 'nullable|array',
            'topics_covered' => 'nullable|array',
            'online_meeting_url' => 'nullable|url',
            'instructor_notes' => 'nullable|string',
        ]);

        $classSession = $this->classSessionService->updateClassSession($classSession, $validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Class session updated successfully',
                'data' => $classSession,
            ]);
        }

        return redirect()->route('class-sessions.index')
            ->with('success', 'Class session updated successfully');
    }

    /**
     * Generate attendance records for all enrolled students in the class session
     */
    public function generateAttendance(Request $request, ClassSession $classSession)
    {
        $result = $this->classSessionService->generateAttendanceForSession($classSession);

        if ($request->expectsJson()) {
            return response()->json($result);
        }

        if ($result['success']) {
            return redirect()->back()
                ->with('success', $result['message']);
        } else {
            return redirect()->back()
                ->with('error', $result['message']);
        }
    }

    /**
     * Remove the specified class session
     */
    public function destroy(Request $request, ClassSession $classSession)
    {
        $this->classSessionService->deleteClassSession($classSession);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Class session deleted successfully',
            ]);
        }

        return redirect()->route('class-sessions.index')
            ->with('success', 'Class session deleted successfully');
    }
}
