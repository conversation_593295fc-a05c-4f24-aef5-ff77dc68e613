<?php

namespace App\Providers;

use App\Services\AcademicLifecycleSeeder\AcademicLifecycleSeeder;
use App\Services\AcademicLifecycleSeeder\SeederConfiguration;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Config;

class AcademicLifecycleSeederServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Bind the main seeder service
        $this->app->singleton(AcademicLifecycleSeeder::class, function ($app) {
            return new AcademicLifecycleSeeder();
        });

        // Bind configuration factory
        $this->app->bind('seeder.configuration', function ($app) {
            $config = Config::get('academic-lifecycle-seeder', []);
            return new SeederConfiguration($config);
        });

        // Register configuration publishing
        $this->mergeConfigFrom(
            __DIR__.'/../../config/academic-lifecycle-seeder.php',
            'academic-lifecycle-seeder'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../../config/academic-lifecycle-seeder.php' => config_path('academic-lifecycle-seeder.php'),
            ], 'academic-lifecycle-seeder-config');
        }

        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\RunAcademicLifecycleSeeder::class,
            ]);
        }
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            AcademicLifecycleSeeder::class,
            'seeder.configuration',
        ];
    }
}
