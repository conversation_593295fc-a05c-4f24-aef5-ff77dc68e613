<script setup lang="ts">
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import type { ClassSession } from '@/types/models';
import { formatDate } from '@/utils/date';
import { attendanceRoutes } from '@/utils/routes';
import { Head, router } from '@inertiajs/vue3';
import { toast } from 'vue-sonner';
import { 
    ArrowLeft, 
    Calendar, 
    Clock, 
    Edit, 
    MapPin, 
    Users, 
    Video, 
    FileText, 
    User,
    CheckCircle,
    AlertCircle,
    Target,
    BookOpen,
    UserPlus
} from 'lucide-vue-next';

interface Props {
    session: ClassSession;
}

const props = defineProps<Props>();

const getStatusVariant = (status: string) => {
    switch (status) {
        case 'scheduled':
            return 'default';
        case 'in_progress':
            return 'warning';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'destructive';
        default:
            return 'secondary';
    }
};

const getDeliveryModeIcon = (mode: string) => {
    return mode === 'online' ? Video : MapPin;
};

const navigateBack = () => {
    router.visit('/class-sessions');
};

const editSession = () => {
    router.visit(attendanceRoutes.classSessions.edit(props.session.id));
};

const viewAttendance = () => {
    router.visit(`/attendance?session_id=${props.session.id}`);
};

// Generate attendance for a session
const generateAttendance = () => {
    router.post(`/class-sessions/${props.session.id}/generate-attendance`, {}, {
        onSuccess: (page) => {
            // Check if there's a success message in the flash data
            if (page.props.flash?.success) {
                toast.success(page.props.flash.success);
            } else {
                toast.success('Attendance records generated successfully');
            }
        },
        onError: (errors) => {
            console.error('Generate attendance errors:', errors);
            // Check if there's an error message in the flash data
            if (errors.flash?.error) {
                toast.error(errors.flash.error);
            } else {
                toast.error('Failed to generate attendance records');
            }
        },
    });
};
</script>

<template>
    <Head :title="`${session.session_title} - Class Session Details`" />

    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
                <Button variant="ghost" size="sm" @click="navigateBack">
                    <ArrowLeft class="mr-2 h-4 w-4" />
                    Back to Sessions
                </Button>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">{{ session.session_title }}</h1>
                    <p class="text-muted-foreground">
                        {{ session.course_offering?.curriculum_unit?.unit.code }} - 
                        {{ session.course_offering?.curriculum_unit?.unit.name }}
                    </p>
                </div>
            </div>
            <div class="flex items-center gap-2">
                <Button variant="outline" @click="viewAttendance">
                    <Users class="mr-2 h-4 w-4" />
                    View Attendance
                </Button>
                <Button variant="outline" @click="generateAttendance">
                    <UserPlus class="mr-2 h-4 w-4" />
                    Generate Attendance
                </Button>
                <Button @click="editSession">
                    <Edit class="mr-2 h-4 w-4" />
                    Edit Session
                </Button>
            </div>
        </div>

        <!-- Session Overview -->
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
            <!-- Main Details -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Calendar class="h-5 w-5" />
                            Session Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-6">
                        <!-- Session Details -->
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Session Date</label>
                                <div class="flex items-center gap-2">
                                    <Calendar class="h-4 w-4" />
                                    <span class="font-medium">{{ formatDate(session.session_date) }}</span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Time</label>
                                <div class="flex items-center gap-2">
                                    <Clock class="h-4 w-4" />
                                    <span class="font-medium">
                                        {{ session.start_time }} - {{ session.end_time }}
                                        <span class="text-muted-foreground ml-2">({{ session.duration_minutes }} min)</span>
                                    </span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Session Type</label>
                                <Badge variant="outline" class="capitalize">
                                    {{ session.session_type?.replace('_', ' ') }}
                                </Badge>
                            </div>
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Delivery Mode</label>
                                <div class="flex items-center gap-2">
                                    <component :is="getDeliveryModeIcon(session.delivery_mode)" class="h-4 w-4" />
                                    <span class="capitalize">{{ session.delivery_mode?.replace('_', ' ') }}</span>
                                </div>
                            </div>
                        </div>

                        <Separator />

                        <!-- Status and Progress -->
                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Status</label>
                                <Badge :variant="getStatusVariant(session.status)" class="capitalize">
                                    {{ session.status?.replace('_', ' ') }}
                                </Badge>
                            </div>
                            <div class="space-y-2">
                                <label class="text-sm font-medium text-muted-foreground">Attendance Required</label>
                                <div class="flex items-center gap-2">
                                    <component :is="session.attendance_required ? CheckCircle : AlertCircle" 
                                               :class="`h-4 w-4 ${session.attendance_required ? 'text-green-600' : 'text-gray-400'}`" />
                                    <span>{{ session.attendance_required ? 'Yes' : 'No' }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div v-if="session.session_description" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Description</label>
                            <p class="text-sm">{{ session.session_description }}</p>
                        </div>

                        <!-- Online Meeting Details -->
                        <div v-if="session.online_meeting_url && session.delivery_mode !== 'in_person'" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Online Meeting</label>
                            <div class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <Video class="h-4 w-4" />
                                    <a :href="session.online_meeting_url" 
                                       target="_blank" 
                                       class="text-blue-600 hover:underline">
                                        Join Meeting
                                    </a>
                                </div>
                                <div v-if="session.meeting_id" class="text-sm">
                                    <span class="text-muted-foreground">Meeting ID:</span> {{ session.meeting_id }}
                                </div>
                                <div v-if="session.meeting_password" class="text-sm">
                                    <span class="text-muted-foreground">Password:</span> {{ session.meeting_password }}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <!-- Learning Content -->
                <Card v-if="session.learning_objectives?.length || session.topics_covered?.length || session.required_materials?.length">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Target class="h-5 w-5" />
                            Learning Content
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-6">
                        <!-- Learning Objectives -->
                        <div v-if="session.learning_objectives?.length" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Learning Objectives</label>
                            <ul class="list-disc list-inside space-y-1">
                                <li v-for="objective in session.learning_objectives" :key="objective" class="text-sm">
                                    {{ objective }}
                                </li>
                            </ul>
                        </div>

                        <!-- Topics Covered -->
                        <div v-if="session.topics_covered?.length" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Topics Covered</label>
                            <ul class="list-disc list-inside space-y-1">
                                <li v-for="topic in session.topics_covered" :key="topic" class="text-sm">
                                    {{ topic }}
                                </li>
                            </ul>
                        </div>

                        <!-- Required Materials -->
                        <div v-if="session.required_materials?.length" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Required Materials</label>
                            <ul class="list-disc list-inside space-y-1">
                                <li v-for="material in session.required_materials" :key="material" class="text-sm">
                                    {{ material }}
                                </li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>

                <!-- Notes -->
                <Card v-if="session.instructor_notes || session.admin_notes || session.student_instructions">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <FileText class="h-5 w-5" />
                            Notes & Instructions
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div v-if="session.instructor_notes" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Instructor Notes</label>
                            <p class="text-sm bg-muted p-3 rounded-md">{{ session.instructor_notes }}</p>
                        </div>
                        <div v-if="session.admin_notes" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Admin Notes</label>
                            <p class="text-sm bg-muted p-3 rounded-md">{{ session.admin_notes }}</p>
                        </div>
                        <div v-if="session.student_instructions" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Student Instructions</label>
                            <p class="text-sm bg-muted p-3 rounded-md">{{ session.student_instructions }}</p>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Course Information -->
                <Card>
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <BookOpen class="h-5 w-5" />
                            Course Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Course Code</label>
                            <p class="font-medium">{{ session.course_offering?.curriculum_unit?.unit.code }}</p>
                        </div>
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Course Name</label>
                            <p class="font-medium">{{ session.course_offering?.curriculum_unit?.unit.name }}</p>
                        </div>
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Credit Points</label>
                            <p class="font-medium">{{ session.course_offering?.curriculum_unit?.unit.credit_points }}</p>
                        </div>
                    </CardContent>
                </Card>

                <!-- Instructor Information -->
                <Card v-if="session.instructor">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <User class="h-5 w-5" />
                            Instructor
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Name</label>
                            <p class="font-medium">{{ session.instructor.name }}</p>
                        </div>
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Email</label>
                            <p class="text-sm">{{ session.instructor.email }}</p>
                        </div>
                    </CardContent>
                </Card>

                <!-- Attendance Statistics -->
                <Card v-if="session.attendance_stats">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <Users class="h-5 w-5" />
                            Attendance Overview
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600">
                                {{ session.attendance_stats.attendance_percentage }}%
                            </div>
                            <p class="text-sm text-muted-foreground">Attendance Rate</p>
                        </div>
                        
                        <Separator />
                        
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-muted-foreground">Present</span>
                                <Badge variant="success" class="text-xs">
                                    {{ session.attendance_stats.present }}
                                </Badge>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-muted-foreground">Late</span>
                                <Badge variant="warning" class="text-xs">
                                    {{ session.attendance_stats.late }}
                                </Badge>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-muted-foreground">Absent</span>
                                <Badge variant="destructive" class="text-xs">
                                    {{ session.attendance_stats.absent }}
                                </Badge>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-muted-foreground">Excused</span>
                                <Badge variant="secondary" class="text-xs">
                                    {{ session.attendance_stats.excused }}
                                </Badge>
                            </div>
                        </div>

                        <Button variant="outline" size="sm" class="w-full" @click="viewAttendance">
                            <Users class="mr-2 h-4 w-4" />
                            View Detailed Attendance
                        </Button>
                    </CardContent>
                </Card>

                <!-- Assessment Information -->
                <Card v-if="session.is_assessment">
                    <CardHeader>
                        <CardTitle class="flex items-center gap-2">
                            <FileText class="h-5 w-5" />
                            Assessment Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent class="space-y-4">
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Assessment Weight</label>
                            <p class="font-medium">{{ session.assessment_weight }}%</p>
                        </div>
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Duration</label>
                            <p class="font-medium">{{ session.assessment_duration_minutes }} minutes</p>
                        </div>
                        <div v-if="session.assessment_materials_allowed?.length" class="space-y-2">
                            <label class="text-sm font-medium text-muted-foreground">Allowed Materials</label>
                            <ul class="list-disc list-inside space-y-1">
                                <li v-for="material in session.assessment_materials_allowed" :key="material" class="text-xs">
                                    {{ material }}
                                </li>
                            </ul>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    </div>
</template>