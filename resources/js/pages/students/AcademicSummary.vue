<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import OverviewTab from './AcademicSummary/OverviewTab.vue';
import type { Student, StudentAcademicSummary } from '@/types/models';
import { ArrowLeft, Download, FileText, User } from 'lucide-vue-next';
import { router } from '@inertiajs/vue3';
import { ref } from 'vue';

interface Props {
    student: Student;
    academicSummary: StudentAcademicSummary;
}

const props = defineProps<Props>();

const activeTab = ref('overview');
const loading = ref(false);

const goBack = () => {
    router.visit(route('students.show', props.student.id));
};

const exportSummary = () => {
    // TODO: Implement export functionality
    console.log('Export academic summary for student:', props.student.id);
};

const getStatusBadgeVariant = (status: string) => {
    const variants: Record<string, string> = {
        admitted: 'secondary',
        enrolled: 'default',
        active: 'default',
        inactive: 'outline',
        on_leave: 'outline',
        suspended: 'destructive',
        graduated: 'secondary',
        dropped_out: 'outline',
    };
    return variants[status] || 'outline';
};

const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ').toUpperCase();
};
</script>

<template>
    <div>
        <Head :title="`Academic Summary - ${student.full_name}`" />
        
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div class="flex items-center gap-4">
                    <Button variant="ghost" size="sm" @click="goBack" class="flex items-center gap-2">
                        <ArrowLeft class="h-4 w-4" />
                        Back to Student
                    </Button>
                    <div>
                        <h1 class="text-2xl font-bold">Academic Summary</h1>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="text-muted-foreground">{{ student.full_name }}</span>
                            <Badge :variant="getStatusBadgeVariant(student.status) as any">
                                {{ formatStatus(student.status) }}
                            </Badge>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-center gap-2">
                    <Button variant="outline" size="sm" @click="exportSummary" class="flex items-center gap-2">
                        <Download class="h-4 w-4" />
                        Export
                    </Button>
                </div>
            </div>

            <!-- Main Content -->
            <Card>
                <CardContent class="p-0">
                    <Tabs v-model="activeTab" class="w-full">
                        <CardHeader class="pb-0">
                            <TabsList class="grid w-full grid-cols-6">
                                <TabsTrigger value="overview" class="flex items-center gap-2">
                                    <User class="h-4 w-4" />
                                    <span class="hidden sm:inline">Overview</span>
                                </TabsTrigger>
                                <TabsTrigger value="registrations" class="flex items-center gap-2">
                                    <FileText class="h-4 w-4" />
                                    <span class="hidden sm:inline">Registrations</span>
                                </TabsTrigger>
                                <TabsTrigger value="scores" class="flex items-center gap-2">
                                    <FileText class="h-4 w-4" />
                                    <span class="hidden sm:inline">Scores</span>
                                </TabsTrigger>
                                <TabsTrigger value="attendance" class="flex items-center gap-2">
                                    <FileText class="h-4 w-4" />
                                    <span class="hidden sm:inline">Attendance</span>
                                </TabsTrigger>
                                <TabsTrigger value="gpa" class="flex items-center gap-2">
                                    <FileText class="h-4 w-4" />
                                    <span class="hidden sm:inline">GPA</span>
                                </TabsTrigger>
                                <TabsTrigger value="graduation" class="flex items-center gap-2">
                                    <FileText class="h-4 w-4" />
                                    <span class="hidden sm:inline">Graduation</span>
                                </TabsTrigger>
                            </TabsList>
                        </CardHeader>

                        <div class="p-6">
                            <!-- Overview Tab -->
                            <TabsContent value="overview" class="mt-0">
                                <OverviewTab 
                                    :overview="academicSummary.overview" 
                                    :loading="loading"
                                />
                            </TabsContent>

                            <!-- Registrations Tab -->
                            <TabsContent value="registrations" class="mt-0">
                                <div class="text-center py-12">
                                    <FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                    <h3 class="text-lg font-semibold mb-2">Registrations Tab</h3>
                                    <p class="text-muted-foreground">This tab will display course registration information.</p>
                                    <p class="text-muted-foreground text-sm mt-2">Coming soon...</p>
                                </div>
                            </TabsContent>

                            <!-- Scores Tab -->
                            <TabsContent value="scores" class="mt-0">
                                <div class="text-center py-12">
                                    <FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                    <h3 class="text-lg font-semibold mb-2">Scores Tab</h3>
                                    <p class="text-muted-foreground">This tab will display assessment scores and performance.</p>
                                    <p class="text-muted-foreground text-sm mt-2">Coming soon...</p>
                                </div>
                            </TabsContent>

                            <!-- Attendance Tab -->
                            <TabsContent value="attendance" class="mt-0">
                                <div class="text-center py-12">
                                    <FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                    <h3 class="text-lg font-semibold mb-3">Attendance Tab</h3>
                                    <p class="text-muted-foreground">This tab will display attendance records and patterns.</p>
                                    <p class="text-muted-foreground text-sm mt-2">Coming soon...</p>
                                </div>
                            </TabsContent>

                            <!-- GPA Tab -->
                            <TabsContent value="gpa" class="mt-0">
                                <div class="text-center py-12">
                                    <FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                    <h3 class="text-lg font-semibold mb-2">GPA Tab</h3>
                                    <p class="text-muted-foreground">This tab will display GPA calculations and transcript.</p>
                                    <p class="text-muted-foreground text-sm mt-2">Coming soon...</p>
                                </div>
                            </TabsContent>

                            <!-- Graduation Tab -->
                            <TabsContent value="graduation" class="mt-0">
                                <div class="text-center py-12">
                                    <FileText class="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                                    <h3 class="text-lg font-semibold mb-2">Graduation Tab</h3>
                                    <p class="text-muted-foreground">This tab will display graduation progress and requirements.</p>
                                    <p class="text-muted-foreground text-sm mt-2">Coming soon...</p>
                                </div>
                            </TabsContent>
                        </div>
                    </Tabs>
                </CardContent>
            </Card>
        </div>
    </div>
</template>

<style scoped>
/* Custom styles for better mobile responsiveness */
@media (max-width: 640px) {
    .grid-cols-6 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    
    .grid-cols-6 > :nth-child(n+4) {
        grid-column: span 1;
        margin-top: 0.5rem;
    }
}
</style>
