# Swinburne Project Management System

## Product Overview
The Swinburne Project Management System is a comprehensive educational management platform built for university environments. It provides end-to-end management of academic operations across multiple campuses.

## Core Features
- **Multi-campus Management**: Support for multiple university campuses with location data
- **Role-based Access Control**: Granular permissions system for different user types
- **Academic Structure Management**: Programs, specializations, curriculum versions, and units
- **Course Management**: Course offerings, scheduling, and registration
- **User Management**: Students, faculty, and staff with appropriate roles
- **Resource Management**: Buildings, rooms, and scheduling

## Development Status
The project is being developed in 5 sequential phases:
1. **Semester and Curriculum Management** (Completed)
2. **Campus and Classroom Management** (In Progress)
3. **User Management System** (In Progress)
4. **Teaching Management System** (In Progress)
5. **Student Enrollment System** (In Progress)

## Target Users
- **Students**: Course registration, academic progress tracking
- **Faculty**: Class management, grading, student monitoring
- **Administrative Staff**: Program management, teaching assignments
- **System Administrators**: System management, permissions, reporting
