**Product Requirements Document (PRD)**

---

### 📄 Feature: Student Academic Summary in Admin Portal

### 🌐 Project: Education Management System

---

## 1. **Overview**

This feature provides a centralized academic profile page for administrators to view detailed academic progress and records of any student in the system. It mirrors the student portal but adds more context, transparency, and administrative power.

---

## 2. **Goals**

* Give admins and academic officers a complete snapshot of a student's academic journey
* Facilitate support, intervention, and issue resolution
* Enable deep inspection of attendance, registration history, scores, and graduation status

---

## 3. **Actors & Permissions**

| Actor  | Permissions Required                      |
| ------ | ----------------------------------------- |
| Admin  | view\_student\_summary                    |
| Học vụ | view\_student\_summary, manage\_academics |

---

## 4. **User Stories**

### 4.1 As an academic officer

* I want to view all courses a student has registered for, including attempt numbers and status
* I want to view a student's attendance rate per course and per session
* I want to track all scores a student received in a course, with submission/chấm điểm dates
* I want to check if a student is on track to graduate

---

## 5. **Main Page Layout: `Admin Portal → Students → [View] → Academic Summary`**

### Tabs:

1. **Overview**

   * Student ID, full name, email, current status
   * Program, specialization, curriculum version
   * Admission date, expected graduation

2. **Registrations**

   * Table: All `course_registrations`
   * Columns: Course name, semester, registration status, retake?, final grade, completion date

3. **Scores**

   * Grouped by `course_offering`
   * Table per unit: assessment component, type, due date, score %, GPA points, graded\_at
   * Support expandable detail

4. **Attendance**

   * Summary per unit: total sessions, attended, missed, percentage
   * Link to session-wise detail if needed

5. **GPA & Transcript**

   * Table per semester: semester GPA, cumulative GPA, academic standing
   * Dean's list, warnings, probation...

6. **Graduation Tracker**

   * Total credits earned vs. required
   * Has met internship/thesis/English requirements?
   * Flag if student is at risk

---

## 6. **Functional Requirements**

### 6.1 Load Student Summary

* Input: `student_id`
* Output: JSON payload with student + all related data from:

  * `students`, `course_registrations`, `academic_records`
  * `assessment_component_detail_scores`, `attendances`, `gpa_calculations`
  * `graduation_requirements`

### 6.2 View Permissions

* Restricted to admins and academic officers
* Use Laravel policies or middleware guards

### 6.3 Navigation & Filtering

* Allow jump to student summary from anywhere with `student_id`
* Filter by semester, unit, or course\_offering

---

## 7. **Non-functional Requirements**

* Fast loading even with 8+ semesters worth of data
* Pagination or lazy loading for large assessment history
* Mobile-friendly read-only mode

---

## 8. **Dependencies**

* `students`, `course_offerings`, `curriculum_units`
* `assessment_components`, `gpa_calculations`, `academic_records`, `attendances`
* Graduation logic engine (existing or planned)

---

## 9. **Open Questions**

* Should admins be able to override any score or attendance from this screen?
* Should this be exportable as a transcript-style PDF?
* Should comments/disputes be added to scores or attendances here?

---

## 10. **Appendix**

* Related: Student Portal, GPA Policy, Attendance Rules, Graduation Requirement Spec
