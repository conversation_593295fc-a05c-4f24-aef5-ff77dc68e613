**Title:** Seeder Timeline for Simulating Student Academic Lifecycle

**Objective:**
Create a robust and realistic seeder flow to simulate the full academic lifecycle for existing students assigned to curriculum versions, covering teaching, attendance, assessments, academic progress, and GPA implications.

---

## Phase 1: Course Offering Generation

**Goal:** Create course offerings from curriculum units for a given semester.

* Identify all active `curriculum_units` linked to `curriculum_versions` assigned to students.
* For each `curriculum_unit`, create a `course_offering`:

  * Link `semester_id`, `curriculum_unit_id`
  * Assign dummy lecturer (if required)
  * Include `delivery_mode`, `schedule_days`, `schedule_time_start/end`, `location`
  * Generate `syllabus` and `assessment_components` if not present

---

## Phase 2: Student Course Registration

**Goal:** Register each student in the appropriate course offerings for their curriculum.

* Match student `curriculum_version_id` with `curriculum_units`
* For the current semester:

  * Create `course_registrations` per student per course\_offering
  * Insert linked `academic_records` with `grade_status = in_progress`

---

## Phase 3: Group Assignment for Course Offerings

**Goal:** Divide large courses into smaller groups (max 55 students) for session and room allocation.

* For each `course_offering` with > 55 students:

  * Split into multiple logical `section_code` (A, B, C...)
  * Create duplicate `course_offerings` for each group
  * Reassign `course_registration.course_offering_id` for each student based on group

---

## Phase 4: Class Sessions & Attendance

**Goal:** Simulate weekly teaching sessions and attach attendance data.

* For each grouped `course_offering`, generate `class_sessions`:

  * 10-15 sessions per course (lecture, lab, tutorial)
  * Assign `room_id`, `session_date`, `start_time`, `end_time`

* For each `class_session`:

  * Insert `attendances` for registered students
  * Randomize `status` (present, late, excused, absent)

---

## Phase 5: Assessment & Grading

**Goal:** Simulate assessments and student performance.

* For each `assessment_component`, create multiple `assessment_component_details`

  * Assign due dates & weight distribution

* For each student and assessment detail:

  * Create `assessment_component_detail_scores`

    * Simulate submission time, score, lateness, bonus, status

* Finalize `academic_records`:

  * Calculate `final_percentage`, `grade_points`, `completion_status`, etc.
  * Flag failures for retake

---

## Phase 6: GPA & Standing

**Goal:** Reflect student academic progress in GPA and academic standing.

* Generate `gpa_calculations` per student:

  * Types: semester, cumulative
  * Set `academic_standing` based on GPA thresholds (probation, good, honors)

* Insert/update `academic_standings`:

  * Include reason, effective date, created\_by

---

## Additional Suggested Steps:

### Academic Retake:

* For failed units, create retake `course_registrations` in the next semester
* Set `is_retake = 1`, increment `attempt_number`

### Program Change:

* Simulate `program_change_requests` and approval
* Reassign student to new curriculum version

### Academic Holds:

* Create a few `academic_holds` (financial, disciplinary, etc.)
* Link to `student_id`, set `status`, `due_date`, `placed_by_user_id`

### Graduation Qualification:

* Check `graduation_requirements`
* If met, set `students.status = graduated`, `academic_status = graduated`