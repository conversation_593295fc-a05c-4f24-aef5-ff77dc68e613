<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Academic Lifecycle Seeder Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the default configuration for the Academic Lifecycle
    | Seeder. These settings can be overridden when executing the seeder.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Target Semesters
    |--------------------------------------------------------------------------
    |
    | Specify which semesters to generate data for. The seeder will create
    | academic data for all specified semesters.
    |
    */
    'target_semesters' => [
        env('SEEDER_TARGET_SEMESTERS', '2024-1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Course Section Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for course sections and enrollment limits.
    |
    */
    'max_students_per_section' => env('SEEDER_MAX_STUDENTS_PER_SECTION', 55),

    /*
    |--------------------------------------------------------------------------
    | Grade Distribution
    |--------------------------------------------------------------------------
    |
    | Percentage distribution for grades. Must sum to 100.
    |
    */
    'grade_distribution' => [
        'A' => 15,
        'B' => 35,
        'C' => 35,
        'D' => 10,
        'F' => 5,
    ],

    /*
    |--------------------------------------------------------------------------
    | Attendance Patterns
    |--------------------------------------------------------------------------
    |
    | Percentage distribution for attendance patterns. Must sum to 100.
    |
    */
    'attendance_patterns' => [
        'excellent' => 20, // 90-100% attendance
        'good' => 50,      // 75-89% attendance
        'average' => 25,   // 60-74% attendance
        'poor' => 5,       // Below 60% attendance
    ],

    /*
    |--------------------------------------------------------------------------
    | Special Cases
    |--------------------------------------------------------------------------
    |
    | Configuration for special academic scenarios.
    |
    */
    'special_cases' => [
        'retake_percentage' => 10,        // Percentage of students who retake failed units
        'program_change_percentage' => 5,  // Percentage of students who change programs
        'academic_hold_percentage' => 3,   // Percentage of students with academic holds
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Enable or disable specific seeder features.
    |
    */
    'include_retakes' => env('SEEDER_INCLUDE_RETAKES', true),
    'include_program_changes' => env('SEEDER_INCLUDE_PROGRAM_CHANGES', true),
    'include_academic_holds' => env('SEEDER_INCLUDE_ACADEMIC_HOLDS', true),

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for seeder performance and execution.
    |
    */
    'performance' => [
        'batch_size' => env('SEEDER_BATCH_SIZE', 100),
        'enable_progress_reporting' => env('SEEDER_ENABLE_PROGRESS', true),
        'parallel_processing' => env('SEEDER_PARALLEL_PROCESSING', false),
        'memory_limit' => env('SEEDER_MEMORY_LIMIT', '512M'),
        'timeout' => env('SEEDER_TIMEOUT', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Settings
    |--------------------------------------------------------------------------
    |
    | Different configurations based on environment.
    |
    */
    'environments' => [
        'development' => [
            'target_semesters' => ['2024-1'],
            'max_students_per_section' => 25,
            'performance' => [
                'batch_size' => 50,
                'enable_progress_reporting' => true,
            ],
        ],
        'testing' => [
            'target_semesters' => ['2024-1'],
            'max_students_per_section' => 20,
            'grade_distribution' => [
                'A' => 20, 'B' => 30, 'C' => 30, 'D' => 15, 'F' => 5
            ],
            'performance' => [
                'batch_size' => 25,
                'enable_progress_reporting' => false,
            ],
        ],
        'staging' => [
            'target_semesters' => ['2024-1', '2024-2'],
            'max_students_per_section' => 55,
            'performance' => [
                'batch_size' => 100,
                'enable_progress_reporting' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for seeder logging and monitoring.
    |
    */
    'logging' => [
        'enabled' => env('SEEDER_LOGGING_ENABLED', true),
        'level' => env('SEEDER_LOG_LEVEL', 'info'),
        'channel' => env('SEEDER_LOG_CHANNEL', 'single'),
        'include_sql_queries' => env('SEEDER_LOG_SQL', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Quality Settings
    |--------------------------------------------------------------------------
    |
    | Settings to control the quality and realism of generated data.
    |
    */
    'data_quality' => [
        'realistic_performance_variation' => true,
        'seasonal_attendance_patterns' => true,
        'prerequisite_enforcement' => true,
        'graduation_timeline_validation' => true,
    ],
];
